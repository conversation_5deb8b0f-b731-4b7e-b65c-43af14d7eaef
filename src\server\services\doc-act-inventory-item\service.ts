import { NatsProtocol } from "@2people-it/inwave-erp-types";
import crypto from "node:crypto";

import * as LocalUtils from "../../utils/index.js";
import * as Types from "../../types/index.js";

import * as Response from "./response.js";
import BaseService from "../base-service.js";

type PreparedDAII =
	NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.SaveSpec.Params["items"][0] & {
		internalCodeId: number;
	};

export default class Service extends BaseService {
	#businessError;
	#logger;
	#repository;
	#transactions;

	constructor(data: {
		businessError: Types.System.BusinessError.Service;
		dal: Types.ServiceLocator.default["dal"];
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#businessError = data.businessError;
		this.#logger = data.logger;
		this.#repository = data.dal.repository.docActInventoryItem;
		this.#transactions = data.dal.transactions;
	}

	#calcOptionHash(options: string[] | null) {
		if (!options || !options.length) return null;
		const optionsAsText = options.sort((a, b) => (a > b ? -1 : 1)).join(",");

		const sha1 = crypto.createHash("sha1");

		sha1.update(optionsAsText);

		return sha1.digest("hex");
	}

	#generateItemHash(
		item: NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.SaveSpec.Params["items"][0],
	) {
		return `${item.nomenclatureItemId}:${
			item.docBillOfMaterialHeaderId
		}:${this.#calcOptionHash(item.options)}`;
	}

	#check = {
		before: {
			get: async (
				payload: NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.GetSpec.Params,
			): Promise<Types.Common.TDataError<boolean>> => {
				const { headerId } = payload;

				{
					const entity
						= await this.services.docActInventoryHeader.innerSpace.getEntityForCheck(
							{ id: headerId },
						);

					if (entity.error) return { error: entity.error };
				}

				return { data: true };
			},
			save: async (
				payload: NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.SaveSpec.Params,
			): Promise<Types.Common.TDataError<PreparedDAII[]>> => {
				const { id, items } = payload;

				const entity
					= await this.services.docActInventoryHeader.innerSpace.getEntity({
						id,
					});

				if (entity.error) return { error: entity.error };

				{
					const isCanSaveSpec
						= await this.services.docActInventoryHeader.commonCheck.isCanSaveSpec(
							entity.data.system_status,
						);

					if (isCanSaveSpec.error) return { error: isCanSaveSpec.error };
				}

				// Check orderNumber
				const isOrderNumberCorrect
					= LocalUtils.OrderNumber.checkOrderNumberSequence(items);

				if (!isOrderNumberCorrect) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"SPEC_ORDER_NUMBER_INCORRECT",
					);
				}

				const itemsFormatted: PreparedDAII[] = [];
				const classifierCategorieIdSet = new Set<string>();

				if (items.length) {
					const itemsSet = new Set<string>();
					const uniqueItemHashSet = new Set<string>();

					for (const item of items) {
						const itemHash = this.#generateItemHash(item);

						if (uniqueItemHashSet.has(itemHash)) {
							return this.#businessError.error.UNKNOWN_ERROR(
								"FORBIDDEN_TO_SAVE_NOT_UNIQUE_SPEC_ITEM_" + itemHash,
							);
						}

						uniqueItemHashSet.add(itemHash);

						itemsSet.add(item.nomenclatureItemId);
					}

					const { data: nomenclatureItems, error }
						= await this.services.nomenclatureItem.innerSpace.getList({
							filters: { ids: Array.from(itemsSet) },
						});

					if (error) return { error };

					for (const item of items) {
						const found = nomenclatureItems.list.find(
							(nomenclatureItem) =>
								nomenclatureItem.id === item.nomenclatureItemId,
						);

						if (!found) {
							return this.#businessError.error.UNKNOWN_ERROR(
								"DOC_ACT_INVENTORY_INVALID_ITEM_IN_PAYLOAD",
							);
						}

						if (entity.data.classifier_category_id) {
							classifierCategorieIdSet.add(found.classifierCategoryId);
						}

						itemsFormatted.push({
							internalCodeId: found.internalCode,
							nomenclatureItemId: item.nomenclatureItemId,
							docBillOfMaterialHeaderId: item.docBillOfMaterialHeaderId,
							selectedIncomingSeries: item.selectedIncomingSeries,
							options: item.options,
							costPrice: item.costPrice,
							quantity: item.quantity,
							orderNumber: item.orderNumber,
						});
					}
				}

				// Если набрали перечень категорий, их надо проверить на принадлежность
				if (classifierCategorieIdSet.size) {
					const classifierCategories
						= await this.services.classifierCategory.innerSpace.getEntitiesForCheck(
							{ ids: Array.from(classifierCategorieIdSet) },
						);

					if (
						classifierCategories.some(
							(category) =>
								entity.data.classifier_category_id
								&& !category.path.includes(entity.data.classifier_category_id),
						)
					) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"CLASSIFIER_CATEGORY_CORRESPONDENCE_IS_VIOLATED",
						);
					}
				}

				return { data: itemsFormatted };
			},
		},
	};

	innerSpace = {
		getSpec: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.GetSpec.Params,
		): Promise<
			Types.Common.TDataError<Types.Dal.DocActInventoryItem.Types.ListEntity[]>
		> => {
			try {
				const data = await this.#repository.getList({
					docHeaderId: payload.headerId,
				});

				return { data };
			} catch (e) {
				return {
					error: { code: -1, message: String(e) },
				};
			}
		},
	};

	outerSpace = {
		getSpec: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.GetSpec.Params,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.GetSpec.Result>
		> => {
			const check = await this.#check.before.get(payload);

			if (check.error) return { error: check.error };

			const { data: list, error } = await this.innerSpace.getSpec({
				headerId: payload.headerId,
			});

			if (error) {
				return {
					error,
				};
			}

			return { data: list.map(Response.getListEntity) };
		},
		saveSpec: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.SaveSpec.Params,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocActInventoryItem.SaveSpec.Result>
		> => {
			const check = await this.#check.before.save(payload);

			if (check.error) return { error: check.error };

			let errorMessage;

			const data = await this.#transactions
				["doc-act-inventory-items-save"]({
					docActInventoryHeaderId: payload.id,
					items: check.data.map((e) => ({
						internal_code: e.internalCodeId,
						nomenclature_item_id: e.nomenclatureItemId,
						selectedIncomingSeries: e.selectedIncomingSeries?.length
							? e.selectedIncomingSeries.map((x) => ({
								serieQuantity: x.quantity,
								incomingSerieId: x.incomingSerieId,
							  }))
							: undefined,
						original_quantity: "0",
						quantity: e.quantity,
						cost_price: e.costPrice || "0",
						doc_bill_of_material_header_id: null,
						fabricated_product_id: null,
						order_number: e.orderNumber,
						options: null,
					})),
				})
				.catch((e) => {
					errorMessage = e.message;
					this.#logger.warn(errorMessage);
				});

			if (!data) return this.#businessError.error.UNKNOWN_ERROR(errorMessage);

			return { data: true };
		},
	};
}
