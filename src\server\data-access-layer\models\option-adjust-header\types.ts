import InwaveTypes from "@2people-it/inwave-erp-types";

export type TableFields = {
	id: string;

	option_id: string;

	title: string;
	version_number: number;

	system_status: number;

	created_at: Date;
	updated_at: Date;

	is_deleted: boolean;
	deleted_at: Date | null;
};

export type TableKeys = keyof TableFields;

export type SearchFields = Partial<TableFields>;

export type EntityForCheck = Pick<TableFields, "id" | "system_status" | "option_id">;

export type Entity = Pick<
	TableFields,
	| "id"
	| "option_id"
	| "title"
	| "version_number"
	| "system_status"
	| "created_at"
	| "updated_at"
>;

export type CreateFields = Pick<
	TableFields,
	"option_id" | "title" | "version_number" | "system_status"
>;

export type UpdateFields = Partial<
	Pick<
		TableFields,
		"title" | "system_status" | "updated_at" | "is_deleted" | "deleted_at"
	>
>;

export type PreparedListOrderField =
	keyof typeof InwaveTypes.Dto.Nats.WarehouseDomain.OptionAdjustHeader.Params.preparedListOrderField;
