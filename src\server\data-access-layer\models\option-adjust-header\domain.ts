import { DbManager } from "@2people-it/inwave-ms-wrapper";

import * as Types from "./types.js";
import { Model } from "./model.js";

type PreparedListSearchFields =
	DbManager.PG.DomainTypes.TSearchParams<Types.SearchFields>;

export default class Domain extends DbManager.PG.BaseDomain<{
	Model: Model;
	CreateFields: Types.CreateFields;
	SearchFields: Types.SearchFields;
	TableFields: Types.TableFields;
	UpdateFields: Types.UpdateFields;
}> {
	constructor(creds: DbManager.PG.ModelTypes.TDBCreds) {
		super({ model: new Model(creds) });
	}

	#preparedListOrderFieldMapping: Record<Types.PreparedListOrderField, Types.TableKeys>
		= {
			id: "id",
			versionNumber: "version_number",
			updatedAt: "updated_at",
		};

	#prepareListOrder(
		order: {
			orderDirection: DbManager.Types.TOrdering;
			orderField: Types.PreparedListOrderField;
		}[],
	) {
		const updatedArr = order.map((item) => {
			const orderDirection = item.orderDirection;
			const orderField = this.#preparedListOrderFieldMapping[item.orderField];

			if (!orderField) throw new Error(`Invalid order field ${item.orderField}`);

			return { orderDirection, orderField };
		});

		return updatedArr;
	}

	async getEntityForCheck(data: { id?: string; }) {
		const { one } = await super.getOneByParams({
			params: {
				id: data.id,
				is_deleted: false,
			},
			selected: ["id", "system_status", "option_id"],
		});

		return one satisfies Types.EntityForCheck | undefined;
	}

	async getEntitiesForCheck(data: { ids: string[]; }) {
		return super.getArrByParams({
			params: {
				id: { $in: data.ids },
				is_deleted: false,
			},
			selected: ["id", "system_status", "option_id"],
		}) satisfies Promise<Types.EntityForCheck[]>;
	}

	async getEntity(data: { id: string; }) {
		const { one } = await super.getOneByParams({
			params: {
				id: data.id,
				is_deleted: false,
			},
		});

		return one satisfies Types.Entity | undefined;
	}

	async getList(data: {
		order?: {
			orderDirection: DbManager.Types.TOrdering;
			orderField: Types.PreparedListOrderField;
		}[];
		pagination?: DbManager.Types.TPagination;
		params: {
			ids?: string[];
			searchQuery?: string;
			optionId?: string;
			isActualVersion?: boolean;
			systemStatus?: number;
		};
	}) {
		const order = data.order ?? [{ orderField: "id", orderDirection: "DESC" }];

		const {
			ids,
			isActualVersion,
			optionId,
			searchQuery,
			systemStatus,
		} = data.params;

		const params: PreparedListSearchFields = {
			id: ids && { $in: ids },
			title: searchQuery ? { $ilike: `%${searchQuery}%` } : undefined,
			option_id: optionId,
			is_actual_version: isActualVersion,
			system_status: systemStatus,
			is_deleted: false,
		};

		return await Promise.all([
			super.getArrByParams({
				params,
				pagination: data.pagination,
				order: this.#prepareListOrder(order).map((e) => ({
					orderBy: e.orderField,
					ordering: e.orderDirection,
				})),
			}) satisfies Promise<Types.Entity[]>,

			super.getCountByParams({ params }),
		]);
	}

	async getNextVersionNumber(data: { optionId: string; }) {
		const [entity] = await super.getArrByParams({
			params: {
				option_id: data.optionId,
			},
			order: [{ orderBy: "version_number", ordering: "DESC" }],
			pagination: {
				limit: 1,
				offset: 0,
			},
			selected: ["version_number"],
		});

		if (entity) return entity.version_number + 1;

		return 1;
	}
}
