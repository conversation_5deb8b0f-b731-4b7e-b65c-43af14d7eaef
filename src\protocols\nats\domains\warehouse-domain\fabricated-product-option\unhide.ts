import { Static } from "@sinclair/typebox";

import { FabricatedProductOption } from "../../../../../dto/nats/warehouse-domain";
import BaseNatsMethod from "../../../base-nats-method";
import { domainNames } from "../../../../../common";

export const method = new BaseNatsMethod({
	domain: domainNames.WarehouseDomain,
	name: "warehouse-domain:fabricated-product-option/unhide",
	params: FabricatedProductOption.Params.unhide,
	result: FabricatedProductOption.Result.entity,
	journalEventDescription: "Опция раскрыта",
});

export type Params = Static<typeof method.params>;
export type Result = Static<typeof method.result>;
