import { Static } from "@sinclair/typebox";

import { FabricatedProductOptionGroup } from "../../../../../dto/nats/warehouse-domain";
import BaseNatsMethod from "../../../base-nats-method";
import { domainNames } from "../../../../../common";

export const method = new BaseNatsMethod({
	domain: domainNames.WarehouseDomain,
	name: "warehouse-domain:fabricated-product-option-group/unhide",
	params: FabricatedProductOptionGroup.Params.unhide,
	result: FabricatedProductOptionGroup.Result.entity,
	journalEventDescription: "Группа опций раскрыта",
});

export type Params = Static<typeof method.params>;
export type Result = Static<typeof method.result>;
