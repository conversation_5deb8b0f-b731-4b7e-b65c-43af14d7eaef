import { DbManager } from "@2people-it/inwave-ms-wrapper";

import * as Types from "./types.js";

const tableName = "option_adjust_headers";
const primaryKey = "id";
const createField = { title: "created_at", type: "timestamp" } as const;
const updateField = { title: "updated_at", type: "timestamp" } as const;
const tableFields: Types.TableKeys[] = [
	"id",

	"option_id",

	"title",
	"version_number",

	"system_status",

	"created_at",
	"updated_at",

	"is_deleted",
	"deleted_at",
];

export class Model extends DbManager.PG.BaseModel {
	constructor(creds: DbManager.PG.ModelTypes.TDBCreds) {
		super(
			{
				createField,
				primaryKey,
				tableFields,
				tableName,
				updateField,
			},
			creds,
		);
	}
}
