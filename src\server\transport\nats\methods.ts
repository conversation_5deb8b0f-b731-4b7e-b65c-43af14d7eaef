import { WarehouseDomain } from "@2people-it/inwave-erp-types/dist/protocols/nats/domains/index.js";

import * as Types from "../../types/index.js";

export const workInProgress = async () => {
	return {
		error: { code: -10000, message: "WORK_IN_PROGRESS" },
	};
};

export const register = (
	services: Types.ServiceLocator.default["services"],
): WarehouseDomain.ProtocolApi => {
	const {
		classifierCategory,
		docActFabricationHeader,
		docActFabricationItem,
		docActInventoryHeader,
		docActInventoryItem,
		docActSupplierOrderHeader,
		docActSupplierOrderItem,
		docActWorkOrderPurchaseHeader,
		docActWorkOrderPurchaseItem,
		docBillOfMaterialHeader,
		docBillOfMaterialItem,
		docInternalMovementHeader,
		docInternalMovementItem,
		docInvoicePurchaseHeader,
		docInvoicePurchaseItem,
		docInvoiceSaleHeader,
		docInvoiceSaleItem,
		fabricatedProduct,
		fabricatedProductOption,
		fabricatedProductOptionGroup,
		healthCheck,
		incomingSerie,
		nomenclatureItem,
		nomenclatureItemAnalogue,
		optionAdjustHeader,
		optionAdjustItem,
		serialNumber,
		supplierNomenclatureItem,
		warehouse,
	} = services;

	return {
		[WarehouseDomain.ClassifierCategory.ChangeParentCategory.method.name]: classifierCategory.outerSpace.changeParentCategory.bind(classifierCategory),
		[WarehouseDomain.ClassifierCategory.Create.method.name]: classifierCategory.outerSpace.create.bind(classifierCategory),
		[WarehouseDomain.ClassifierCategory.GetEntity.method.name]: classifierCategory.outerSpace.getEntity.bind(classifierCategory),
		[WarehouseDomain.ClassifierCategory.GetHierarchy.method.name]: classifierCategory.outerSpace.getHierarchy.bind(classifierCategory),
		[WarehouseDomain.ClassifierCategory.GetList.method.name]: classifierCategory.outerSpace.getList.bind(classifierCategory),
		[WarehouseDomain.ClassifierCategory.Remove.method.name]: classifierCategory.outerSpace.remove.bind(classifierCategory),
		[WarehouseDomain.ClassifierCategory.Update.method.name]: classifierCategory.outerSpace.update.bind(classifierCategory),

		[WarehouseDomain.DocActFabricationHeader.Accept.method.name]: docActFabricationHeader.outerSpace.accept.bind(docActFabricationHeader),
		[WarehouseDomain.DocActFabricationHeader.Create.method.name]: docActFabricationHeader.outerSpace.create.bind(docActFabricationHeader),
		[WarehouseDomain.DocActFabricationHeader.GetEntity.method.name]: docActFabricationHeader.outerSpace.getEntity.bind(docActFabricationHeader),
		[WarehouseDomain.DocActFabricationHeader.GetList.method.name]: docActFabricationHeader.outerSpace.getList.bind(docActFabricationHeader),
		[WarehouseDomain.DocActFabricationHeader.GetPreparedList.method.name]: docActFabricationHeader.outerSpace.getPreparedList.bind(docActFabricationHeader),
		[WarehouseDomain.DocActFabricationHeader.Update.method.name]: docActFabricationHeader.outerSpace.update.bind(docActFabricationHeader),

		[WarehouseDomain.DocActFabricationItem.GetSpec.method.name]: docActFabricationItem.outerSpace.getSpec.bind(docActFabricationItem),
		[WarehouseDomain.DocActFabricationItem.SaveSpec.method.name]: docActFabricationItem.outerSpace.saveSpec.bind(docActFabricationItem),

		[WarehouseDomain.DocActInventoryHeader.Accept.method.name]: docActInventoryHeader.outerSpace.accept.bind(docActInventoryHeader),
		[WarehouseDomain.DocActInventoryHeader.Create.method.name]: docActInventoryHeader.outerSpace.create.bind(docActInventoryHeader),
		[WarehouseDomain.DocActInventoryHeader.Decline.method.name]: docActInventoryHeader.outerSpace.decline.bind(docActInventoryHeader),
		[WarehouseDomain.DocActInventoryHeader.GetEntity.method.name]: docActInventoryHeader.outerSpace.getEntity.bind(docActInventoryHeader),
		[WarehouseDomain.DocActInventoryHeader.GetList.method.name]: docActInventoryHeader.outerSpace.getList.bind(docActInventoryHeader),
		[WarehouseDomain.DocActInventoryHeader.GetPreparedList.method.name]: docActInventoryHeader.outerSpace.getPreparedList.bind(docActInventoryHeader),
		[WarehouseDomain.DocActInventoryHeader.Start.method.name]: docActInventoryHeader.outerSpace.start.bind(docActInventoryHeader),
		[WarehouseDomain.DocActInventoryHeader.Update.method.name]: docActInventoryHeader.outerSpace.update.bind(docActInventoryHeader),

		[WarehouseDomain.DocActInventoryItem.GetSpec.method.name]: docActInventoryItem.outerSpace.getSpec.bind(docActInventoryItem),
		[WarehouseDomain.DocActInventoryItem.SaveSpec.method.name]: docActInventoryItem.outerSpace.saveSpec.bind(docActInventoryItem),

		[WarehouseDomain.DocActSupplierOrderHeader.Accept.method.name]: docActSupplierOrderHeader.outerSpace.accept.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.Create.method.name]: docActSupplierOrderHeader.outerSpace.create.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.CreateWithSpec.method.name]: docActSupplierOrderHeader.outerSpace.createWithSpec.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.Decline.method.name]: docActSupplierOrderHeader.outerSpace.decline.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.GetEntity.method.name]: docActSupplierOrderHeader.outerSpace.getEntity.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.GetConsolidatedList.method.name]: docActSupplierOrderHeader.outerSpace.getConsolidatedList.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.GetList.method.name]: docActSupplierOrderHeader.outerSpace.getList.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.GetPreparedList.method.name]: docActSupplierOrderHeader.outerSpace.getPreparedList.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.GetProgress.method.name]: docActSupplierOrderHeader.outerSpace.getProgress.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.Start.method.name]: docActSupplierOrderHeader.outerSpace.start.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.Update.method.name]: docActSupplierOrderHeader.outerSpace.update.bind(docActSupplierOrderHeader),
		[WarehouseDomain.DocActSupplierOrderHeader.Remove.method.name]: docActSupplierOrderHeader.outerSpace.remove.bind(docActSupplierOrderHeader),

		[WarehouseDomain.DocActSupplierOrderItem.GetSpec.method.name]: docActSupplierOrderItem.outerSpace.getSpec.bind(docActSupplierOrderItem),
		[WarehouseDomain.DocActSupplierOrderItem.SaveSpec.method.name]: docActSupplierOrderItem.outerSpace.saveSpec.bind(docActSupplierOrderItem),

		[WarehouseDomain.DocActWorkOrderPurchaseHeader.Accept.method.name]: docActWorkOrderPurchaseHeader.outerSpace.accept.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.Create.method.name]: docActWorkOrderPurchaseHeader.outerSpace.create.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.Decline.method.name]: docActWorkOrderPurchaseHeader.outerSpace.decline.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.GetAssociatedList.method.name]: docActWorkOrderPurchaseHeader.outerSpace.getAssociatedList.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.GetEntity.method.name]: docActWorkOrderPurchaseHeader.outerSpace.getEntity.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.GetList.method.name]: docActWorkOrderPurchaseHeader.outerSpace.getList.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.GetPreparedList.method.name]: docActWorkOrderPurchaseHeader.outerSpace.getPreparedList.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.Start.method.name]: docActWorkOrderPurchaseHeader.outerSpace.start.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.Update.method.name]: docActWorkOrderPurchaseHeader.outerSpace.update.bind(docActWorkOrderPurchaseHeader),
		[WarehouseDomain.DocActWorkOrderPurchaseHeader.Remove.method.name]: docActWorkOrderPurchaseHeader.outerSpace.remove.bind(docActWorkOrderPurchaseHeader),

		[WarehouseDomain.DocActWorkOrderPurchaseItem.GetSpec.method.name]: docActWorkOrderPurchaseItem.outerSpace.getSpec.bind(docActWorkOrderPurchaseItem),
		[WarehouseDomain.DocActWorkOrderPurchaseItem.SaveSpec.method.name]: docActWorkOrderPurchaseItem.outerSpace.saveSpec.bind(docActWorkOrderPurchaseItem),

		[WarehouseDomain.DocInvoicePurchaseHeader.Accept.method.name]: docInvoicePurchaseHeader.outerSpace.accept.bind(docInvoicePurchaseHeader),
		[WarehouseDomain.DocInvoicePurchaseHeader.Create.method.name]: docInvoicePurchaseHeader.outerSpace.create.bind(docInvoicePurchaseHeader),
		[WarehouseDomain.DocInvoicePurchaseHeader.Decline.method.name]: docInvoicePurchaseHeader.outerSpace.decline.bind(docInvoicePurchaseHeader),
		[WarehouseDomain.DocInvoicePurchaseHeader.GetEntity.method.name]: docInvoicePurchaseHeader.outerSpace.getEntity.bind(docInvoicePurchaseHeader),
		[WarehouseDomain.DocInvoicePurchaseHeader.GetList.method.name]: docInvoicePurchaseHeader.outerSpace.getList.bind(docInvoicePurchaseHeader),
		[WarehouseDomain.DocInvoicePurchaseHeader.GetPreparedList.method.name]: docInvoicePurchaseHeader.outerSpace.getPreparedList.bind(docInvoicePurchaseHeader),
		[WarehouseDomain.DocInvoicePurchaseHeader.Update.method.name]: docInvoicePurchaseHeader.outerSpace.update.bind(docInvoicePurchaseHeader),

		[WarehouseDomain.DocInvoicePurchaseItem.GetSpec.method.name]: docInvoicePurchaseItem.outerSpace.getSpec.bind(docInvoicePurchaseItem),
		[WarehouseDomain.DocInvoicePurchaseItem.SaveSpec.method.name]: docInvoicePurchaseItem.outerSpace.saveSpec.bind(docInvoicePurchaseItem),

		[WarehouseDomain.DocInternalMovementHeader.Create.method.name]: docInternalMovementHeader.outerSpace.create.bind(docInternalMovementHeader),
		[WarehouseDomain.DocInternalMovementHeader.Decline.method.name]: docInternalMovementHeader.outerSpace.decline.bind(docInternalMovementHeader),
		[WarehouseDomain.DocInternalMovementHeader.Forward.method.name]: docInternalMovementHeader.outerSpace.forward.bind(docInternalMovementHeader),
		[WarehouseDomain.DocInternalMovementHeader.GetEntity.method.name]: docInternalMovementHeader.outerSpace.getEntity.bind(docInternalMovementHeader),
		[WarehouseDomain.DocInternalMovementHeader.GetList.method.name]: docInternalMovementHeader.outerSpace.getList.bind(docInternalMovementHeader),
		[WarehouseDomain.DocInternalMovementHeader.GetPreparedList.method.name]: docInternalMovementHeader.outerSpace.getPreparedList.bind(docInternalMovementHeader),
		[WarehouseDomain.DocInternalMovementHeader.Reserve.method.name]: docInternalMovementHeader.outerSpace.reserve.bind(docInternalMovementHeader),
		[WarehouseDomain.DocInternalMovementHeader.Take.method.name]: docInternalMovementHeader.outerSpace.take.bind(docInternalMovementHeader),
		[WarehouseDomain.DocInternalMovementHeader.Update.method.name]: docInternalMovementHeader.outerSpace.update.bind(docInternalMovementHeader),

		[WarehouseDomain.DocInternalMovementItem.GetSpec.method.name]: docInternalMovementItem.outerSpace.getSpec.bind(docInternalMovementItem),
		[WarehouseDomain.DocInternalMovementItem.SaveSpec.method.name]: docInternalMovementItem.outerSpace.saveSpec.bind(docInternalMovementItem),

		[WarehouseDomain.DocInvoiceSaleHeader.Accept.method.name]: docInvoiceSaleHeader.outerSpace.accept.bind(docInvoiceSaleHeader),
		[WarehouseDomain.DocInvoiceSaleHeader.Create.method.name]: docInvoiceSaleHeader.outerSpace.create.bind(docInvoiceSaleHeader),
		[WarehouseDomain.DocInvoiceSaleHeader.Decline.method.name]: docInvoiceSaleHeader.outerSpace.decline.bind(docInvoiceSaleHeader),
		[WarehouseDomain.DocInvoiceSaleHeader.GetEntity.method.name]: docInvoiceSaleHeader.outerSpace.getEntity.bind(docInvoiceSaleHeader),
		[WarehouseDomain.DocInvoiceSaleHeader.GetList.method.name]: docInvoiceSaleHeader.outerSpace.getList.bind(docInvoiceSaleHeader),
		[WarehouseDomain.DocInvoiceSaleHeader.GetPreparedList.method.name]: docInvoiceSaleHeader.outerSpace.getPreparedList.bind(docInvoiceSaleHeader),
		[WarehouseDomain.DocInvoiceSaleHeader.Update.method.name]: docInvoiceSaleHeader.outerSpace.update.bind(docInvoiceSaleHeader),

		[WarehouseDomain.DocInvoiceSaleItem.GetSpec.method.name]: docInvoiceSaleItem.outerSpace.getSpec.bind(docInvoiceSaleItem),
		[WarehouseDomain.DocInvoiceSaleItem.SaveSpec.method.name]: docInvoiceSaleItem.outerSpace.saveSpec.bind(docInvoiceSaleItem),

		[WarehouseDomain.HealthCheck.GetStatus.method.name]: healthCheck.outerSpace.getStatus.bind(healthCheck),

		[WarehouseDomain.NomenclatureItem.ChangeParentCategory.method.name]: nomenclatureItem.outerSpace.changeParentCategory.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.Create.method.name]: nomenclatureItem.outerSpace.create.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.GetBalance.method.name]: nomenclatureItem.outerSpace.getBalance.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.GetBreadCrumbs.method.name]: nomenclatureItem.outerSpace.getBreadCrumbs.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.GetEntity.method.name]: nomenclatureItem.outerSpace.getEntity.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.GetList.method.name]: nomenclatureItem.outerSpace.getList.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.GetPreparedList.method.name]: nomenclatureItem.outerSpace.getPreparedList.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.Remove.method.name]: nomenclatureItem.outerSpace.remove.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.SearchByNonFormalizedTitle.method.name]: nomenclatureItem.outerSpace.searchByNonFormalizedTitle.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.Update.method.name]: nomenclatureItem.outerSpace.update.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.GenerateSpecSheet.method.name]: nomenclatureItem.outerSpace.generateSpecSheet.bind(nomenclatureItem),
		[WarehouseDomain.NomenclatureItem.GeneratePurchaseSheet.method.name]: nomenclatureItem.outerSpace.generatePurchaseSheet.bind(nomenclatureItem),

		[WarehouseDomain.IncomingSerie.GetFinalizedList.method.name]: incomingSerie.outerSpace.getFinalizedList.bind(incomingSerie),
		[WarehouseDomain.IncomingSerie.GetList.method.name]: incomingSerie.outerSpace.getList.bind(incomingSerie),
		[WarehouseDomain.IncomingSerie.GetPreparedList.method.name]: incomingSerie.outerSpace.getPreparedList.bind(incomingSerie),

		[WarehouseDomain.NomenclatureItemAnalogue.Create.method.name]: nomenclatureItemAnalogue.outerSpace.create.bind(nomenclatureItemAnalogue),
		[WarehouseDomain.NomenclatureItemAnalogue.GetEntity.method.name]: nomenclatureItemAnalogue.outerSpace.getEntity.bind(nomenclatureItemAnalogue),
		[WarehouseDomain.NomenclatureItemAnalogue.GetList.method.name]: nomenclatureItemAnalogue.outerSpace.getList.bind(nomenclatureItemAnalogue),
		[WarehouseDomain.NomenclatureItemAnalogue.GetPreparedList.method.name]: nomenclatureItemAnalogue.outerSpace.getPreparedList.bind(nomenclatureItemAnalogue),
		[WarehouseDomain.NomenclatureItemAnalogue.Remove.method.name]: nomenclatureItemAnalogue.outerSpace.remove.bind(nomenclatureItemAnalogue),
		[WarehouseDomain.NomenclatureItemAnalogue.Update.method.name]: nomenclatureItemAnalogue.outerSpace.update.bind(nomenclatureItemAnalogue),

		[WarehouseDomain.SupplierNomenclatureItem.Create.method.name]: supplierNomenclatureItem.outerSpace.create.bind(supplierNomenclatureItem),
		[WarehouseDomain.SupplierNomenclatureItem.GetEntity.method.name]: supplierNomenclatureItem.outerSpace.getEntity.bind(supplierNomenclatureItem),
		[WarehouseDomain.SupplierNomenclatureItem.GetList.method.name]: supplierNomenclatureItem.outerSpace.getList.bind(supplierNomenclatureItem),
		[WarehouseDomain.SupplierNomenclatureItem.GetPreparedList.method.name]: supplierNomenclatureItem.outerSpace.getPreparedList.bind(supplierNomenclatureItem),
		[WarehouseDomain.SupplierNomenclatureItem.Update.method.name]: supplierNomenclatureItem.outerSpace.update.bind(supplierNomenclatureItem),
		[WarehouseDomain.SupplierNomenclatureItem.Remove.method.name]: supplierNomenclatureItem.outerSpace.remove.bind(supplierNomenclatureItem),

		[WarehouseDomain.Warehouse.ChangePosition.method.name]: warehouse.outerSpace.changePosition.bind(warehouse),
		[WarehouseDomain.Warehouse.Create.method.name]: warehouse.outerSpace.create.bind(warehouse),
		[WarehouseDomain.Warehouse.GetBasicBalance.method.name]: warehouse.outerSpace.getBasicBalance.bind(warehouse),
		[WarehouseDomain.Warehouse.GetEntity.method.name]: warehouse.outerSpace.getEntity.bind(warehouse),
		[WarehouseDomain.Warehouse.GetList.method.name]: warehouse.outerSpace.getList.bind(warehouse),
		[WarehouseDomain.Warehouse.Remove.method.name]: warehouse.outerSpace.remove.bind(warehouse),
		[WarehouseDomain.Warehouse.Update.method.name]: warehouse.outerSpace.update.bind(warehouse),

		[WarehouseDomain.FabricatedProductOption.Create.method.name]: fabricatedProductOption.outerSpace.create.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.GetEntity.method.name]: fabricatedProductOption.outerSpace.getEntity.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.GetList.method.name]: fabricatedProductOption.outerSpace.getList.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.GetPreparedList.method.name]: fabricatedProductOption.outerSpace.getPreparedList.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.GetParentList.method.name]: fabricatedProductOption.outerSpace.getParentList.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.Update.method.name]: fabricatedProductOption.outerSpace.update.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.Remove.method.name]: fabricatedProductOption.outerSpace.remove.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.SelectActualVersion.method.name]: fabricatedProductOption.outerSpace.selectActualVersion.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.Hide.method.name]: fabricatedProductOption.outerSpace.hide.bind(fabricatedProductOption),
		[WarehouseDomain.FabricatedProductOption.Unhide.method.name]: fabricatedProductOption.outerSpace.unhide.bind(fabricatedProductOption),

		[WarehouseDomain.FabricatedProductOptionGroup.Create.method.name]: fabricatedProductOptionGroup.outerSpace.create.bind(fabricatedProductOptionGroup),
		[WarehouseDomain.FabricatedProductOptionGroup.GetEntity.method.name]: fabricatedProductOptionGroup.outerSpace.getEntity.bind(fabricatedProductOptionGroup),
		[WarehouseDomain.FabricatedProductOptionGroup.GetList.method.name]: fabricatedProductOptionGroup.outerSpace.getList.bind(fabricatedProductOptionGroup),
		[WarehouseDomain.FabricatedProductOptionGroup.GetOptions.method.name]: fabricatedProductOptionGroup.outerSpace.getOptions.bind(fabricatedProductOptionGroup),
		[WarehouseDomain.FabricatedProductOptionGroup.GetPreparedList.method.name]: fabricatedProductOptionGroup.outerSpace.getPreparedList.bind(fabricatedProductOptionGroup),
		[WarehouseDomain.FabricatedProductOptionGroup.Remove.method.name]: fabricatedProductOptionGroup.outerSpace.remove.bind(fabricatedProductOptionGroup),
		[WarehouseDomain.FabricatedProductOptionGroup.Update.method.name]: fabricatedProductOptionGroup.outerSpace.update.bind(fabricatedProductOptionGroup),
		[WarehouseDomain.FabricatedProductOptionGroup.Hide.method.name]: fabricatedProductOptionGroup.outerSpace.hide.bind(fabricatedProductOptionGroup),
		[WarehouseDomain.FabricatedProductOptionGroup.Unhide.method.name]: fabricatedProductOptionGroup.outerSpace.unhide.bind(fabricatedProductOptionGroup),

		[WarehouseDomain.OptionAdjustHeader.Accept.method.name]: optionAdjustHeader.outerSpace.accept.bind(optionAdjustHeader),
		[WarehouseDomain.OptionAdjustHeader.Create.method.name]: optionAdjustHeader.outerSpace.create.bind(optionAdjustHeader),
		[WarehouseDomain.OptionAdjustHeader.GetEntity.method.name]: optionAdjustHeader.outerSpace.getEntity.bind(optionAdjustHeader),
		[WarehouseDomain.OptionAdjustHeader.GetList.method.name]: optionAdjustHeader.outerSpace.getList.bind(optionAdjustHeader),
		[WarehouseDomain.OptionAdjustHeader.GetPreparedList.method.name]: optionAdjustHeader.outerSpace.getPreparedList.bind(optionAdjustHeader),
		[WarehouseDomain.OptionAdjustHeader.Update.method.name]: optionAdjustHeader.outerSpace.update.bind(optionAdjustHeader),
		[WarehouseDomain.OptionAdjustHeader.Remove.method.name]: optionAdjustHeader.outerSpace.remove.bind(optionAdjustHeader),

		[WarehouseDomain.OptionAdjustItem.GetSpec.method.name]: optionAdjustItem.outerSpace.getSpec.bind(optionAdjustItem),
		[WarehouseDomain.OptionAdjustItem.SaveSpec.method.name]: optionAdjustItem.outerSpace.saveSpec.bind(optionAdjustItem),

		[WarehouseDomain.DocBillOfMaterialHeader.Copy.method.name]: docBillOfMaterialHeader.outerSpace.copy.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.Create.method.name]: docBillOfMaterialHeader.outerSpace.create.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.GetEntity.method.name]: docBillOfMaterialHeader.outerSpace.getEntity.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.GetList.method.name]: docBillOfMaterialHeader.outerSpace.getList.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.GetPreparedList.method.name]: docBillOfMaterialHeader.outerSpace.getPreparedList.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.GetParentList.method.name]: docBillOfMaterialHeader.outerSpace.getParentList.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.Update.method.name]: docBillOfMaterialHeader.outerSpace.update.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.SelectActualVersion.method.name]: docBillOfMaterialHeader.outerSpace.selectActualVersion.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.Accept.method.name]: docBillOfMaterialHeader.outerSpace.accept.bind(docBillOfMaterialHeader),
		[WarehouseDomain.DocBillOfMaterialHeader.Remove.method.name]: docBillOfMaterialHeader.outerSpace.remove.bind(docBillOfMaterialHeader),

		[WarehouseDomain.DocBillOfMaterialItem.GetSpec.method.name]: docBillOfMaterialItem.outerSpace.getSpec.bind(docBillOfMaterialItem),
		[WarehouseDomain.DocBillOfMaterialItem.SaveSpec.method.name]: docBillOfMaterialItem.outerSpace.saveSpec.bind(docBillOfMaterialItem),

		[WarehouseDomain.FabricatedProduct.Create.method.name]: fabricatedProduct.outerSpace.create.bind(fabricatedProduct),
		[WarehouseDomain.FabricatedProduct.Fabricate.method.name]: fabricatedProduct.outerSpace.fabricate.bind(fabricatedProduct),
		[WarehouseDomain.FabricatedProduct.GetEntity.method.name]: fabricatedProduct.outerSpace.getEntity.bind(fabricatedProduct),
		[WarehouseDomain.FabricatedProduct.GetList.method.name]: fabricatedProduct.outerSpace.getList.bind(fabricatedProduct),
		[WarehouseDomain.FabricatedProduct.GetPreparedList.method.name]: fabricatedProduct.outerSpace.getPreparedList.bind(fabricatedProduct),
		[WarehouseDomain.FabricatedProduct.Update.method.name]: fabricatedProduct.outerSpace.update.bind(fabricatedProduct),
		[WarehouseDomain.FabricatedProduct.Disassemble.method.name]: fabricatedProduct.outerSpace.disassemble.bind(fabricatedProduct),
		[WarehouseDomain.FabricatedProduct.Ship.method.name]: fabricatedProduct.outerSpace.ship.bind(fabricatedProduct),

		[WarehouseDomain.SerialNumber.Create.method.name]: serialNumber.outerSpace.create.bind(serialNumber),
		[WarehouseDomain.SerialNumber.GetEntity.method.name]: serialNumber.outerSpace.getEntity.bind(serialNumber),
		[WarehouseDomain.SerialNumber.GetList.method.name]: serialNumber.outerSpace.getList.bind(serialNumber),
		[WarehouseDomain.SerialNumber.GetPreparedList.method.name]: serialNumber.outerSpace.getPreparedList.bind(serialNumber),
		[WarehouseDomain.SerialNumber.Remove.method.name]: serialNumber.outerSpace.remove.bind(serialNumber),
	} as const;
};
