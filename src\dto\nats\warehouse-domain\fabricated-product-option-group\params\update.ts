import { Static, Type } from "@sinclair/typebox";

import * as BaseEntities from "../../../../../base-entities";
import { <PERSON>hemaHelper } from "../../../../../common";

const FabricatedProductOptionGroup = BaseEntities.WarehouseDomain.FabricatedProductOptionGroup.entity;

export const update = SchemaHelper.StrictObject({
	id: FabricatedProductOptionGroup.id,

	title: Type.Optional(FabricatedProductOptionGroup.title),
	description: Type.Optional(FabricatedProductOptionGroup.description),

	type: Type.Optional(FabricatedProductOptionGroup.type),
	isSelectedValueRequired: Type.Optional(FabricatedProductOptionGroup.isSelectedValueRequired),

	orderNumber: Type.Optional(FabricatedProductOptionGroup.orderNumber),
});

export type Update = Static<typeof update>;
