import { Static } from "@sinclair/typebox";

import { FabricatedProductOption } from "../../../../../dto/http/warehouse-domain";
import BaseMethod from "../../../base-http-method";

export const method = new BaseMethod({
	name: "warehouse-domain/fabricated-product-option/hide",
	params: FabricatedProductOption.Params.hide,
	result: FabricatedProductOption.Result.entity,
	systemPermissions: { admin: true, employee: true, guest: false },
	permissionDescription: "Скрытие опции",
});

export type Params = Static<typeof method.params>;
export type Result = Static<typeof method.result>;
