import InwaveTypes from "@2people-it/inwave-erp-types";
import { WarehouseDomain } from "@2people-it/inwave-erp-types/dist/protocols/nats/domains/index.js";

import * as Response from "./response.js";
import * as Types from "../../types/index.js";
import BaseService from "../base-service.js";
import { SystemStatus as DocBillOfMaterialHeaderSystemStatus } from "../doc-bill-of-material-header/enum/index.js";

const systemStatus
	= InwaveTypes.BaseEntities.WarehouseDomain.OptionAdjustHeader.systemStatus;

export default class Service extends BaseService {
	#businessError;
	#logger;
	#repository;

	constructor(data: {
		businessError: Types.System.BusinessError.Service;
		dal: Types.ServiceLocator.default["dal"];
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#businessError = data.businessError;
		this.#logger = data.logger;
		this.#repository = data.dal.repository.optionAdjustHeader;
	}

	#check = {
		before: {
			accept: async (
				payload: WarehouseDomain.OptionAdjustHeader.Accept.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const entity = await this.innerSpace.getEntityForCheck({
					id: payload.id,
				});

				if (entity.error) return { error: entity.error };

				if (entity.data.system_status !== systemStatus["draft"]) {
					return this.#businessError.error.UNKNOWN_ERROR("MUST_BE_DRAFT");
				}

				const [addSpec, removeSpec] = await Promise.all([
					this.services.optionAdjustItem.innerSpace.getSpec({
						headerId: payload.id,
						specType: "add",
					}),

					this.services.optionAdjustItem.innerSpace.getSpec({
						headerId: payload.id,
						specType: "remove",
					}),
				]);

				if (addSpec.error) return { error: addSpec.error };
				if (removeSpec.error) return { error: removeSpec.error };

				const items = [...addSpec.data, ...removeSpec.data];

				const docBillOfMaterialHeaderIds: string[] = [];

				for (const item of items) {
					const { data, error }
						= await this.services.optionAdjustItem.innerSpace.checkIsCanBeAccepted(
							item,
						);

					if (error) return { error };

					docBillOfMaterialHeaderIds.push(...data.docBillOfMaterialHeaderIds);
				}

				if (docBillOfMaterialHeaderIds.length > 0) {
					const headers
						= await this.services.docBillOfMaterialHeader.innerSpace.getEntitiesForCheck(
							{
								ids: [...new Set(docBillOfMaterialHeaderIds)],
							},
						);

					if (
						headers.some(
							(e) =>
								e.system_status
								!== DocBillOfMaterialHeaderSystemStatus.accepted,
						)
					) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"ALL_DOC_BOM_HEADERS_MUST_BE_ACCEPTED",
						);
					}
				}

				return { data: true };
			},

			create: async (
				payload: WarehouseDomain.OptionAdjustHeader.Create.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { fabricatedProductOptionId } = payload;

				// check for option exists
				{
					const { error }
						= await this.services.fabricatedProductOption.innerSpace.getEntityForCheck(
							{
								id: fabricatedProductOptionId,
							},
						);

					if (error) return { error };
				}

				return { data: true };
			},

			update: async (
				payload: WarehouseDomain.OptionAdjustHeader.Update.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { data: entity, error } = await this.#getEntityForCheck({
					id: payload.id,
				});

				if (error) return { error };

				if (entity.system_status !== systemStatus.draft) {
					return this.#businessError.error.UNKNOWN_ERROR("MUST_BE_DRAFT");
				}

				return { data: true };
			},

			remove: async (
				payload: WarehouseDomain.OptionAdjustHeader.Remove.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const entity = await this.#getEntityForCheck({ id: payload.id });

				if (entity.error) return { error: entity.error };

				return this.#businessError.error.WORK_IN_PROGRESS();

				// return { data: true };
			},
		},
	};

	async #getEntityForCheck(payload: {
		id: string;
	}): Promise<
		Types.Common.TDataError<Types.Dal.OptionAdjustHeader.Types.EntityForCheck>
	> {
		const entity = await this.#repository.getEntityForCheck(payload);

		if (!entity) {
			return this.#businessError.error.ENTITY_NOT_FOUND(
				"NOT_FOUND_OPTION_ADJUST_HEADER",
			);
		}

		return { data: entity };
	}

	async #getEntity(payload: {
		id: string;
	}): Promise<
		Types.Common.TDataError<Types.Dal.OptionAdjustHeader.Types.Entity>
	> {
		const entity = await this.#repository.getEntity(payload);

		if (!entity) {
			return this.#businessError.error.ENTITY_NOT_FOUND(
				"NOT_FOUND_OPTION_ADJUST_HEADER",
			);
		}

		return { data: entity };
	}

	async #getEntitiesForCheck(payload: {
		ids: string[];
	}): Promise<Types.Dal.OptionAdjustHeader.Types.EntityForCheck[]> {
		return await this.#repository.getEntitiesForCheck(payload);
	}

	async #getActualAdjustHeaderIdSet(payload: {
		optionIds: string[];
	}): Promise<Set<string>> {
		const [fabricatedProductOptions]
			= await this.services.fabricatedProductOption.innerSpace.getEntities({
				ids: payload.optionIds,
			});

		const actualAdjustHeaderIdSet = new Set<string>();

		for (const option of fabricatedProductOptions) {
			if (option.actual_adjust_header_id) {
				actualAdjustHeaderIdSet.add(option.actual_adjust_header_id);
			}
		}

		return actualAdjustHeaderIdSet;
	}

	innerSpace = {
		getEntityForCheck: this.#getEntityForCheck.bind(this),
		getEntity: this.#getEntity.bind(this),
		getEntitiesForCheck: this.#getEntitiesForCheck.bind(this),
	};

	queueJobHandlers = {
		create: async (
			payload: WarehouseDomain.OptionAdjustHeader.Create.Params,
		): Promise<{ id: string; }> => {
			const versionNumber = await this.#repository.getNextVersionNumber({
				optionId: payload.fabricatedProductOptionId,
			});

			return await this.#repository.createOne({
				option_id: payload.fabricatedProductOptionId,
				title: payload.title,
				version_number: versionNumber,
				system_status: systemStatus.draft,
			});
		},
	};

	outerSpace = {
		accept: async (
			payload: WarehouseDomain.OptionAdjustHeader.Accept.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustHeader.Accept.Result>
		> => {
			const check = await this.#check.before.accept(payload);

			if (check.error) return { error: check.error };

			await this.#repository.updateOneByPk(payload.id, {
				system_status: systemStatus.accepted,
			});

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		create: async (
			payload: WarehouseDomain.OptionAdjustHeader.Create.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustHeader.Create.Result>
		> => {
			const check = await this.#check.before.create(payload);

			if (check.error) return { error: check.error };

			const { id } = await this.services.queueManager.queues[
				"option-adjust-header-operations"
			].create(payload);

			const entity = await this.#getEntity({ id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		getEntity: async (
			payload: WarehouseDomain.OptionAdjustHeader.GetEntity.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustHeader.GetEntity.Result>
		> => {
			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			const actualAdjustHeaderIdSet = await this.#getActualAdjustHeaderIdSet({
				optionIds: [entity.data.option_id],
			});

			return { data: Response.getEntity(entity.data, actualAdjustHeaderIdSet) };
		},

		getList: async (
			payload: WarehouseDomain.OptionAdjustHeader.GetList.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustHeader.GetList.Result>
		> => {
			const { filters, limit, offset, order } = payload;

			const [list, total] = await this.#repository.getList({
				order,
				pagination: { limit, offset },
				params: {
					...filters,
					optionId: filters.fabricatedProductOptionId,
					systemStatus:
						filters.systemStatus && systemStatus.getByKey(filters.systemStatus),
				},
			});

			const actualAdjustHeaderIdSet = await this.#getActualAdjustHeaderIdSet({
				optionIds: list.map((e) => e.option_id),
			});

			return {
				data: {
					list: list.map((e) => Response.getEntity(e, actualAdjustHeaderIdSet)),
					total,
				},
			};
		},

		getPreparedList: async (
			payload: WarehouseDomain.OptionAdjustHeader.GetPreparedList.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustHeader.GetPreparedList.Result>
		> => {
			const { filters, limit, offset, order } = payload;

			const [list, total] = await this.#repository.getList({
				order,
				pagination: { limit, offset },
				params: {
					...filters,
					optionId: filters.fabricatedProductOptionId,
					systemStatus:
						filters.systemStatus && systemStatus.getByKey(filters.systemStatus),
				},
			});

			const actualAdjustHeaderIdSet = await this.#getActualAdjustHeaderIdSet({
				optionIds: list.map((e) => e.option_id),
			});

			return {
				data: {
					list: list.map((e) => Response.getEntity(e, actualAdjustHeaderIdSet)),
					total,
				},
			};
		},

		update: async (
			payload: WarehouseDomain.OptionAdjustHeader.Update.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustHeader.Update.Result>
		> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			await this.#repository.updateOneByPk(payload.id, {
				title: payload.title,
			});

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			const actualAdjustHeaderIdSet = await this.#getActualAdjustHeaderIdSet({
				optionIds: [entity.data.option_id],
			});

			return { data: Response.getEntity(entity.data, actualAdjustHeaderIdSet) };
		},

		remove: async (
			payload: WarehouseDomain.OptionAdjustHeader.Remove.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustHeader.Remove.Result>
		> => {
			const check = await this.#check.before.remove(payload);

			if (check.error) return { error: check.error };

			await this.#repository.deleteOneByPk(payload.id);

			return {
				data: {
					id: payload.id,
				},
			};
		},
	};
}
