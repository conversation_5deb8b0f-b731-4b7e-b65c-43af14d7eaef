import * as InwaveTypes from "@2people-it/inwave-erp-types";

import * as Enum from "./enum/index.js";
import * as Response from "./response.js";
import * as Types from "../../types/index.js";
import * as Utils from "../../utils/index.js";
import BaseService from "../base-service.js";

export default class Service extends BaseService {
	#businessError;
	#logger;
	#repository;
	#transactions;

	constructor(data: {
		broker: Types.Broker.default;
		businessError: Types.System.BusinessError.Service;
		dal: Types.ServiceLocator.default["dal"];
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#businessError = data.businessError;
		this.#logger = data.logger;
		this.#repository = data.dal.repository.docBillOfMaterialItem;
		this.#transactions = data.dal.transactions;
	}

	#check = {
		before: {
			save: async (
				payload: InwaveTypes.NatsProtocol.Domains.WarehouseDomain.DocBillOfMaterialItem.SaveSpec.Params,
			): Promise<
				Types.Common.TDataError<
					Types.DalActions.DocBillOfMaterial.Types.ItemsSave["items"]
				>
			> => {
				const { headerId, items } = payload;

				const header
					= await this.services.docBillOfMaterialHeader.innerSpace.getEntityForCheck(
						{
							id: headerId,
						},
					);

				if (header.error) return { error: header.error };

				{
					const { error } = await this.#check.isHeaderCanBeUpdated(headerId);

					if (error) return { error };
				}

				const preparedItems: Types.DalActions.DocBillOfMaterial.Types.ItemsSave["items"]
					= [];

				if (items.length) {
					const documentationNomenclatureItemIdSet = new Set<string>();

					const userIds: string[] = [];
					const allOptionIds: string[] = []; // options to check existence
					const headerIds: string[] = [];
					const warehouseIds: string[] = [];
					const companyIds: string[] = [];

					const specItemOptionIdsMap = new Map<string, string[]>();

					for (const item of items) {
						const optionIds = item.options.map((e) => e.id);

						allOptionIds.push(...optionIds);

						if (item.nomenclatureItemId && optionIds.length) {
							if (!specItemOptionIdsMap.has(item.nomenclatureItemId)) {
								specItemOptionIdsMap.set(item.nomenclatureItemId, []);
							}

							specItemOptionIdsMap
								.get(item.nomenclatureItemId)
								?.push(...optionIds);
						}

						if (item.source) {
							switch (item.source.type) {
								case "warehouse": {
									if (item.source.warehouseId) {
										warehouseIds.push(item.source.warehouseId);
									}

									break;
								}

								case "purchase": {
									if (item.source.companyId) {
										companyIds.push(item.source.companyId);
									}

									if (item.source.managerId) {
										userIds.push(item.source.managerId);
									}

									break;
								}

								case "fabrication": {
									if (item.source.docBillOfMaterialHeaderId) {
										headerIds.push(item.source.docBillOfMaterialHeaderId);
									}

									if (item.source.warehouseId) {
										warehouseIds.push(item.source.warehouseId);
									}

									break;
								}
							}
						}
					}

					{
						const { error } = await this.#check.isUsersExist([
							...new Set(userIds),
						]);

						if (error) return { error };
					}

					{
						const { error } = await this.#check.isOptionsExist([
							...new Set(allOptionIds),
						]);

						if (error) return { error };
					}

					for (const [nomenclatureItemId, optionIds] of specItemOptionIdsMap) {
						const { error } = await this.#check.isOptionsForNomenclatureItem({
							optionIds: [...new Set(optionIds)],
							nomenclatureItemId,
						});

						if (error) return { error };
					}

					{
						const { error } = await this.#check.isHeadersExist([
							...new Set(headerIds),
						]);

						if (error) return { error };
					}

					{
						const { error } = await this.#check.isWarehousesExist([
							...new Set(warehouseIds),
						]);

						if (error) return { error };
					}

					{
						const { error } = await this.#check.isCompaniesExist([
							...new Set(companyIds),
						]);

						if (error) return { error };
					}

					const nomenclatureItemIds = [
						...new Set(
							items
								.map((item) => item.nomenclatureItemId)
								.filter(Utils.isTruthyValue),
						),
					];

					{
						const { error } = await this.#check.isNotRecursiveDependent({
							nomenclatureItemId: header.data.nomenclature_item_id,
							specNomenclatureItemIds: nomenclatureItemIds,
							headerIds: [...new Set(headerIds)],
						});

						if (error) return { error };
					}

					const nomenclatureItems
						= await this.services.nomenclatureItem.innerSpace.getList({
							filters: {
								ids: nomenclatureItemIds,
							},
						});

					if (nomenclatureItems.error)
						return { error: nomenclatureItems.error };

					const nomenclatureItemMap = new Map(
						nomenclatureItems.data.list.map((e) => [e.id, e]),
					);

					for (const item of items) {
						if (item.nomenclatureItemId) {
							// Номенклатурные позиции в разделе Документация должны быть уникальными.
							if (
								item.section
								=== InwaveTypes.Common.specItemSection.constEnum["documentation"]
							) {
								if (
									documentationNomenclatureItemIdSet.has(
										item.nomenclatureItemId,
									)
								) {
									return this.#businessError.error.UNKNOWN_ERROR(
										"DOCUMENTATION_NOMENCLATURE_ITEMS_MUST_BE_UNIQUE",
									);
								}

								documentationNomenclatureItemIdSet.add(item.nomenclatureItemId);
							}

							const nomenclatureItem = nomenclatureItemMap.get(
								item.nomenclatureItemId,
							);

							if (!nomenclatureItem) {
								return this.#businessError.error.ENTITY_NOT_FOUND(
									"NOT_FOUND_NOMENCLATURE_ITEM",
								);
							}

							if (item.source?.type === "fabrication") {
								if (!nomenclatureItem.isFabricableProduct) {
									return this.#businessError.error.UNKNOWN_ERROR(
										"MATERIAL_CANNOT_BE_FABRICATED",
									);
								}
							}

							if (
								nomenclatureItem.isFabricableProduct
								&& item.quantity
								&& !InwaveTypes.Utils.Micros.isInteger(item.quantity)
							) {
								return this.#businessError.error.UNKNOWN_ERROR(
									"FABRICABLE_PRODUCT_QUANTITY_MUST_BE_INTEGER",
								);
							}
						}

						const sourceType = item.source?.type
							? Enum.SourceType[item.source.type]
							: null;

						preparedItems.push({
							...item,
							section: InwaveTypes.Common.specItemSection[item.section],
							source: {
								warehouseId: null,
								companyId: null,
								docBillOfMaterialHeaderId: null,
								managerId: null,
								...item.source,
								type: sourceType,
							},
							orderNumber: preparedItems.length,
						});
					}
				}

				return {
					data: preparedItems,
				};
			},
		},

		isHeaderCanBeUpdated: async (
			headerId: string,
		): Promise<Types.Common.TDataError<true>> => {
			const entity
				= await this.services.docBillOfMaterialHeader.innerSpace.getEntityForCheck(
					{ id: headerId },
				);

			if (entity.error) return { error: entity.error };

			const isCanBeUpdated
				= await this.services.docBillOfMaterialHeader.innerSpace.checkIsCanBeUpdated(
					entity.data.system_status,
				);

			if (isCanBeUpdated.error) return { error: isCanBeUpdated.error };

			return { data: true };
		},

		isUsersExist: async (
			userIds: string[],
		): Promise<Types.Common.TDataError<true>> => {
			if (userIds.length) {
				const { error } = await this.services.relatedEntity.checkUsers({
					userIds,
				});

				if (error) {
					return this.#businessError.error.ENTITY_NOT_FOUND("NOT_FOUND_USER");
				}
			}

			return { data: true };
		},

		isOptionsExist: async (
			optionIds: string[],
		): Promise<Types.Common.TDataError<true>> => {
			if (optionIds.length) {
				const options
					= await this.services.fabricatedProductOption.innerSpace.getEntitiesForCheck(
						{
							ids: optionIds,
						},
					);

				if (optionIds.length !== options.length) {
					return this.#businessError.error.ENTITY_NOT_FOUND(
						"NOT_FOUND_FABRICATED_PRODUCT_OPTION",
					);
				}
			}

			return { data: true };
		},

		isHeadersExist: async (
			headerIds: string[],
		): Promise<Types.Common.TDataError<true>> => {
			if (headerIds.length) {
				const headers
					= await this.services.docBillOfMaterialHeader.innerSpace.getEntitiesForCheck(
						{
							ids: headerIds,
						},
					);

				if (headerIds.length !== headers.length) {
					return this.#businessError.error.ENTITY_NOT_FOUND(
						"NOT_FOUND_DOC_BILL_OF_MATERIAL_HEADER",
					);
				}
			}

			return { data: true };
		},

		isWarehousesExist: async (
			warehouseIds: string[],
		): Promise<Types.Common.TDataError<true>> => {
			if (warehouseIds.length) {
				const result
					= await this.services.warehouse.innerSpace.getEntitiesForCheck({
						ids: warehouseIds,
					});

				if (result.error) {
					return result;
				}
			}

			return { data: true };
		},

		isCompaniesExist: async (
			companyIds: string[],
		): Promise<Types.Common.TDataError<true>> => {
			if (companyIds.length) {
				const { error } = await this.services.relatedEntity.checkCompanies({
					companyIds,
				});

				if (error) {
					return this.#businessError.error.ENTITY_NOT_FOUND(
						"NOT_FOUND_COMPANY",
					);
				}
			}

			return { data: true };
		},

		isNotRecursiveDependent: async (payload: {
			nomenclatureItemId: string;
			specNomenclatureItemIds: string[];
			headerIds: string[];
		}): Promise<Types.Common.TDataError<true>> => {
			let headerIds = [...payload.headerIds];

			if (
				payload.specNomenclatureItemIds.includes(payload.nomenclatureItemId)
			) {
				return this.#businessError.error.UNKNOWN_ERROR("RECURSION_NOT_ALLOWED");
			}

			while (headerIds.length > 0) {
				const items = await this.#repository.getList({ headerIds });

				headerIds = [];

				for (const item of items) {
					if (item.nomenclature_item_id === payload.nomenclatureItemId) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"RECURSION_NOT_ALLOWED",
						);
					}

					if (
						item.source_type === Enum.SourceType.fabrication
						&& item.source_doc_bill_of_material_header_id
					) {
						headerIds.push(item.source_doc_bill_of_material_header_id);
					}
				}
			}

			return { data: true };
		},

		isCanBeAccepted: (
			item: Types.Dal.DocBillOfMaterialItem.Types.ListEntity,
		): Types.Common.TDataError<{ headerIds: string[]; }> => {
			if (item.quantity === null) {
				return this.#businessError.error.UNKNOWN_ERROR("QUANTITY_MUST_BE_SET");
			}

			const headerIds: string[] = [];

			if (item.nomenclature_item_doc_bill_of_material_header_id) {
				headerIds.push(item.nomenclature_item_doc_bill_of_material_header_id);
			}

			switch (item.source_type) {
				case Enum.SourceType.fabrication: {
					if (item.source_doc_bill_of_material_header_id) {
						headerIds.push(item.source_doc_bill_of_material_header_id);
					}

					break;
				}
			}

			return {
				data: {
					headerIds,
				},
			};
		},

		isOptionsForNomenclatureItem: async (payload: {
			optionIds: string[];
			nomenclatureItemId: string;
		}): Promise<Types.Common.TDataError<true>> => {
			if (payload.optionIds.length === 0) {
				return {
					data: true,
				};
			}

			const [options]
				= await this.services.fabricatedProductOption.innerSpace.getEntities({
					ids: payload.optionIds,
					nomenclatureItemIds: [payload.nomenclatureItemId],
				});

			if (options.length !== payload.optionIds.length) {
				return this.#businessError.error.UNKNOWN_ERROR(
					"INCORRECT_OPTIONS_FOR_NOMENCLATURE_ITEM",
				);
			}

			return { data: true };
		},
	};

	innerSpace = {
		getSpec: async (payload: { headerId: string; }) => {
			return await this.#repository.getList({
				headerIds: [payload.headerId],
			});
		},

		checkIsCanBeAccepted: this.#check.isCanBeAccepted.bind(this),
	};

	outerSpace = {
		getSpec: async (
			payload: InwaveTypes.NatsProtocol.Domains.WarehouseDomain.DocBillOfMaterialItem.GetSpec.Params,
		): Promise<
			Types.Common.TDataError<InwaveTypes.NatsProtocol.Domains.WarehouseDomain.DocBillOfMaterialItem.GetSpec.Result>
		> => {
			const list = await this.#repository.getList({
				headerIds: [payload.headerId],
			});

			return {
				data: list.map(Response.getListEntity),
			};
		},

		saveSpec: async (
			payload: InwaveTypes.NatsProtocol.Domains.WarehouseDomain.DocBillOfMaterialItem.SaveSpec.Params,
		): Promise<
			Types.Common.TDataError<InwaveTypes.NatsProtocol.Domains.WarehouseDomain.DocBillOfMaterialItem.SaveSpec.Result>
		> => {
			const check = await this.#check.before.save(payload);

			if (check.error) return { error: check.error };

			await this.#transactions["doc-bill-of-material-items-save"]({
				docBillOfMaterialHeaderId: payload.headerId,
				items: check.data,
			});

			return { data: true };
		},
	};
}
