import { WarehouseDomain } from "@2people-it/inwave-erp-types/dist/protocols/nats/domains/index.js";

import * as Enum from "./enum/index.js";
import * as Response from "./response.js";
import * as Types from "../../types/index.js";
import BaseService from "../base-service.js";

export default class Service extends BaseService {
	#businessError;
	#logger;
	#repository;
	#transactions;

	constructor(data: {
		businessError: Types.System.BusinessError.Service;
		dal: Types.ServiceLocator.default["dal"];
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#businessError = data.businessError;
		this.#logger = data.logger;
		this.#repository = data.dal.repository.fabricatedProductOptionGroup;
		this.#transactions = data.dal.transactions;
	}

	#check = {
		before: {
			create: async (
				payload: WarehouseDomain.FabricatedProductOptionGroup.Create.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { nomenclatureItemId } = payload;

				// check for nomenclature item exists
				{
					const { data: nomenclatureItem, error }
						= await this.services.nomenclatureItem.innerSpace.getEntityForCheck({
							id: nomenclatureItemId,
						});

					if (error) return { error };

					if (!nomenclatureItem.is_fabricable_product) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"NOMENCLATURE_ITEM_IS_NOT_FABRICABLE_PRODUCT",
						);
					}
				}

				return { data: true };
			},

			getOptions: async (
				payload: WarehouseDomain.FabricatedProductOptionGroup.GetOptions.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { nomenclatureItemId } = payload;

				// check for nomenclature item exists
				{
					const { error }
						= await this.services.nomenclatureItem.innerSpace.getEntityForCheck({
							id: nomenclatureItemId,
						});

					if (error) return { error };
				}

				return { data: true };
			},

			update: async (
				payload: WarehouseDomain.FabricatedProductOptionGroup.Update.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const entity = await this.#getEntityForCheck({ id: payload.id });

				if (entity.error) return { error: entity.error };

				return { data: true };
			},

			remove: async (
				payload: WarehouseDomain.FabricatedProductOptionGroup.Remove.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const entity = await this.#getEntityForCheck({ id: payload.id });

				if (entity.error) return { error: entity.error };

				return { data: true };
			},
		},

		isConfigurationAllowed: async (payload: {
			nomenclatureItemId: string;
			optionIds: string[];
		}): Promise<Types.Common.TDataError<true>> => {
			const [allGroups] = await this.#repository.getList({
				params: {
					nomenclatureItemIds: [payload.nomenclatureItemId],
				},
			});

			const [selectedGroups] = await this.#repository.getList({
				params: {
					optionIds: payload.optionIds,
				},
			});

			const selectedGroupIdSet = new Set(
				selectedGroups.map((group) => group.id),
			);

			for (const group of allGroups) {
				if (
					group.is_selected_value_required
					&& !selectedGroupIdSet.has(group.id)
				) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"SELECTED_NOT_ALL_REQUIRED_GROUPS",
					);
				}
			}

			const optionIdSet = new Set(payload.optionIds);

			for (const group of selectedGroups) {
				if (group.nomenclature_item_id !== payload.nomenclatureItemId) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"OPTIONS_MUST_BE_FOR_NOMENCLATURE_ITEM",
					);
				}

				const selectedOptionIds = group.option_ids.filter((optionId) =>
					optionIdSet.has(optionId),
				);

				if (
					group.type === Enum.Type.exclusive
					&& selectedOptionIds.length > 1
				) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"EXCLUSIVE_GROUP_ALLOWS_ONLY_ONE_OPTION",
					);
				}
			}

			const [options]
				= await this.services.fabricatedProductOption.innerSpace.getEntities({
					ids: payload.optionIds,
				});

			for (const option of options) {
				if (
					option.incompatible_option_ids.some((optionId) =>
						optionIdSet.has(optionId),
					)
				) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"SELECTED_INCOMPATIBLE_OPTIONS",
					);
				}

				if (
					option.required_option_ids.some(
						(optionId) => !optionIdSet.has(optionId),
					)
				) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"SELECTED_NOT_ALL_REQUIRED_OPTIONS",
					);
				}
			}

			return { data: true };
		},
	};

	async #getEntityForCheck(payload: {
		id: string;
	}): Promise<
		Types.Common.TDataError<Types.Dal.FabricatedProductOptionGroup.Types.EntityForCheck>
	> {
		const entity = await this.#repository.getEntityForCheck(payload);

		if (!entity) {
			return this.#businessError.error.ENTITY_NOT_FOUND(
				"NOT_FOUND_FABRICATED_PRODUCT_OPTION_GROUP",
			);
		}

		return { data: entity };
	}

	async #getEntity(payload: {
		id: string;
	}): Promise<
		Types.Common.TDataError<Types.Dal.FabricatedProductOptionGroup.Types.Entity>
	> {
		const entity = await this.#repository.getEntity(payload);

		if (!entity) {
			return this.#businessError.error.ENTITY_NOT_FOUND(
				"NOT_FOUND_FABRICATED_PRODUCT_OPTION_GROUP",
			);
		}

		return { data: entity };
	}

	innerSpace = {
		getEntityForCheck: async (payload: { id: string; }) =>
			this.#getEntityForCheck(payload),

		getEntitiesForCheck: async (payload: { ids: string[]; }) =>
			this.#repository.getEntitiesForCheck(payload),

		checkIsConfigurationAllowed: async (payload: {
			nomenclatureItemId: string;
			optionIds: string[];
		}) => this.#check.isConfigurationAllowed(payload),
	};

	queueJobHandlers = {
		create: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Create.Params,
		): Promise<Types.Common.TDataError<{ id: string; }>> => {
			const check = await this.#check.before.create(payload);

			if (check.error) return { error: check.error };

			const orderNumber = await this.#repository.getNextOrderNumber({
				nomenclatureItemId: payload.nomenclatureItemId,
			});

			const created = await this.#repository.createOne({
				nomenclature_item_id: payload.nomenclatureItemId,
				title: payload.title,
				description: payload.description,
				type: Enum.Type[payload.type],
				is_selected_value_required: payload.isSelectedValueRequired,
				is_hidden: payload.isHidden,
				order_number: orderNumber,
			});

			return {
				data: created,
			};
		},

		update: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Update.Params,
		): Promise<Types.Common.TDataError<{ id: string; }>> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			await this.#transactions["fabricated-product-option-group-update"]({
				id: payload.id,

				title: payload.title,
				description: payload.description,

				type: payload.type && Enum.Type[payload.type],
				isSelectedValueRequired: payload.isSelectedValueRequired,

				orderNumber: payload.orderNumber,
			});

			return {
				data: {
					id: payload.id,
				},
			};
		},

		hide: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Hide.Params,
		): Promise<Types.Common.TDataError<{ id: string; }>> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			await this.#transactions["fabricated-product-option-group-hide"]({
				id: payload.id,
			});

			return {
				data: {
					id: payload.id,
				},
			};
		},

		unhide: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Unhide.Params,
		): Promise<Types.Common.TDataError<{ id: string; }>> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			await this.#transactions["fabricated-product-option-group-unhide"]({
				id: payload.id,
			});

			return {
				data: {
					id: payload.id,
				},
			};
		},
	};

	outerSpace = {
		create: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Create.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.Create.Result>
		> => {
			const check = await this.#check.before.create(payload);

			if (check.error) return { error: check.error };

			const created = await this.services.queueManager.queues[
				"fabricated-product-option-group-operations"
			].exec({
				type: "create",
				payload,
			});

			if (created.error) return { error: created.error };

			const entity = await this.#getEntity({ id: created.data.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		getEntity: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.GetEntity.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.GetEntity.Result>
		> => {
			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		getList: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.GetList.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.GetList.Result>
		> => {
			const { filters, limit, offset, order } = payload;

			const [list, total] = await this.#repository.getList({
				order,
				pagination: { limit, offset },
				params: filters,
			});

			return {
				data: {
					list: list.map(Response.getListEntity),
					total,
				},
			};
		},

		getPreparedList: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.GetPreparedList.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.GetPreparedList.Result>
		> => {
			const { filters, limit, offset, order } = payload;

			const [list, total] = await this.#repository.getList({
				order,
				pagination: { limit, offset },
				params: filters,
			});

			return {
				data: {
					list: list.map(Response.getPreparedListEntity),
					total,
				},
			};
		},

		getOptions: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.GetOptions.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.GetOptions.Result>
		> => {
			const check = await this.#check.before.getOptions(payload);

			if (check.error) return { error: check.error };

			const options = await this.#repository.getOptions({
				nomenclature_item_id: payload.nomenclatureItemId,
				is_hidden: payload.isHidden,
			});

			return { data: options.map(Response.getOptionsEntity) };
		},

		update: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Update.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.Update.Result>
		> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			const updated = await this.services.queueManager.queues[
				"fabricated-product-option-group-operations"
			].exec({
				type: "update",
				payload,
			});

			if (updated.error) return { error: updated.error };

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		hide: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Hide.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.Hide.Result>
		> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			const updated = await this.services.queueManager.queues[
				"fabricated-product-option-group-operations"
			].exec({
				type: "hide",
				payload,
			});

			if (updated.error) return { error: updated.error };

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		unhide: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Unhide.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.Unhide.Result>
		> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			const updated = await this.services.queueManager.queues[
				"fabricated-product-option-group-operations"
			].exec({
				type: "unhide",
				payload,
			});

			if (updated.error) return { error: updated.error };

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		remove: async (
			payload: WarehouseDomain.FabricatedProductOptionGroup.Remove.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOptionGroup.Remove.Result>
		> => {
			const check = await this.#check.before.remove(payload);

			if (check.error) return { error: check.error };

			return this.#businessError.error.WORK_IN_PROGRESS();

			/* await this.#transactions["fabricated-product-option-group-remove"](
				payload,
			);

			return {
				data: {
					id: payload.id,
				},
			}; */
		},
	};
}
