import { Db<PERSON>anager } from "@2people-it/inwave-ms-wrapper";

import * as TransactionTypes from "../../types/index.js";
import * as Types from "../../../../types/index.js";

export default async function (
	options: {
		pool: DbManager.PG.BaseModel["pool"];
		repository: Types.ServiceLocator.default["dal"]["repository"];
	},
	payload: TransactionTypes.FabricatedProductOptionGroupHide,
) {
	const { pool, repository } = options;

	const client = await pool.connect();

	try {
		await client.query("BEGIN");

		// Update group to hide it
		const { query, values } = DbManager.PG.BaseModel.getUpdateFields<
			Types.Dal.FabricatedProductOptionGroup.Types.UpdateFields,
			Types.Dal.FabricatedProductOptionGroup.Types.TableKeys
		>({
			params: {
				is_hidden: true,
			},
			tableName: repository.fabricatedProductOptionGroup.tableName,
			primaryKey: { field: "id", value: payload.id },
			returning: ["id"],
		});

		await client.query(query, values);

		await client.query("COMMIT");

		return { id: payload.id };
	} catch (e) {
		await client.query("ROLLBACK");
		throw e;
	} finally {
		client.release();
	}
}
