{"name": "inwave-erp-warehouse-domain", "version": "1.63.0", "type": "module", "main": "./build/server/index.js", "scripts": {"build": "tsc --build", "clean": "<PERSON><PERSON><PERSON> build", "check": "tsc --project tsconfig.json", "dev:local": "npm run clean && tsc-watch --onSuccess \" nodemon .\"", "lint": "npm run check && eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "migrations:up": "node ./build/migrations/up.js", "migrations:down": "node ./build/migrations/down.js", "migrations:create-empty-sql": "node ./build/migrations/create-empty-sql.js", "start": "npm run migrations:up && node .", "test:prepare": "npm run clean && npm run build", "test:local": "node ./build/test/index.js", "test:static": "eslint . --ext .ts --max-warnings 0 && npx tsc --skipLib<PERSON><PERSON>ck", "prepare": "husky install"}, "author": "", "license": "ISC", "devDependencies": {"@semantic-release/changelog": "6.0.3", "@semantic-release/exec": "6.0.3", "@semantic-release/git": "10.0.1", "@semantic-release/gitlab": "12.0.6", "@semantic-release/npm": "11.0.0", "@types/node": "20.3.2", "@typescript-eslint/eslint-plugin": "5.60.1", "@typescript-eslint/parser": "5.60.1", "dotenv": "16.3.1", "eslint": "8.43.0", "eslint-plugin-sort-destructure-keys": "1.5.0", "eslint-plugin-sort-exports": "0.8.0", "husky": "8.0.3", "nodemon": "2.0.22", "prettier": "2.5.1", "prettier-eslint": "16.3.0", "rimraf": "5.0.1", "semantic-release-discord-bot": "1.1.0", "tsc-watch": "6.0.4", "typescript": "5.2.2"}, "dependencies": {"@2people-it/inwave-erp-types": "1.199.0", "@2people-it/inwave-ms-wrapper": "1.43.126", "bullmq": "5.7.12"}}