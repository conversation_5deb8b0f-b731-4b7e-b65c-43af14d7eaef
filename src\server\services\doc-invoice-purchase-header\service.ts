import {
	Common as CommonTypes,
	NatsProtocol,
	Utils,
} from "@2people-it/inwave-erp-types";
import { PoolClient } from "@2people-it/inwave-ms-wrapper/build/lib/transactions/types/index.js";

import * as Types from "../../types/index.js";

import * as Enum from "./enum/index.js";
import * as Response from "./response.js";
import BaseService from "../base-service.js";
import { TDataError } from "../../types/common/index.js";

export default class Service extends BaseService {
	#PREFIX = "whs002-";

	#businessError;
	#logger;
	#repository;
	#transactions;

	constructor(data: {
		businessError: Types.System.BusinessError.Service;
		dal: Types.ServiceLocator.default["dal"];
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#businessError = data.businessError;
		this.#logger = data.logger;
		this.#repository = data.dal.repository.docInvoicePurchaseHeader;
		this.#transactions = data.dal.transactions;
	}

	#check = {
		before: {
			create: async (
				payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Create.Params,
				meta?: CommonTypes.Types.NatsProtocolApiMeta,
			): Promise<Types.Common.TDataError<true>> => {
				const { companyId, relevanceDate, warehouseId } = payload;

				if (!meta) {
					return this.#businessError.error.ACCESS_DENIED();
				}

				{
					const { error } = this.#check.isRelevanceDateCorrect(relevanceDate);

					if (error) {
						return {
							error,
						};
					}
				}

				// Check for user are permitted (admin or warehouse manager)
				{
					const { error }
						= await this.services.warehouse.innerSpace.checkWarehouseOwner(
							meta,
							warehouseId,
						);

					if (error) {
						return {
							error,
						};
					}
				}

				{
					const { error } = await this.services.relatedEntity.checkCompanies({
						companyIds: [companyId],
					});

					if (error) return { error };
				}

				{
					const { error }
						= await this.services.warehouse.innerSpace.getEntityForCheck({
							id: warehouseId,
						});

					if (error) return { error };
				}

				return { data: true };
			},
			update: async (
				payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Update.Params,
				meta?: CommonTypes.Types.NatsProtocolApiMeta,
			): Promise<Types.Common.TDataError<true>> => {
				const { companyId, id, relevanceDate } = payload;

				if (!meta) {
					return this.#businessError.error.ACCESS_DENIED();
				}

				{
					const { error } = this.#check.isRelevanceDateCorrect(relevanceDate);

					if (error) {
						return {
							error,
						};
					}
				}

				const entity = await this.#getEntity({ id });

				if (entity.error) return { error: entity.error };

				// Check for user are permitted (admin or warehouse manager)
				{
					const { error }
						= await this.services.warehouse.innerSpace.checkWarehouseOwner(
							meta,
							entity.data.warehouse_id,
						);

					if (error) {
						return {
							error,
						};
					}
				}

				{
					const { error } = this.#check.isCanBeUpdated(
						entity.data.system_status,
					);

					if (error) return { error };
				}

				{
					const { error } = await this.services.relatedEntity.checkCompanies({
						companyIds: [companyId],
					});

					if (error) return { error };
				}

				return { data: true };
			},
			accept: async (
				payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Accept.Params,
				meta?: CommonTypes.Types.NatsProtocolApiMeta,
				transactionClient?: PoolClient,
			): Promise<
				Types.Common.TDataError<{
					entity: Types.Dal.DocInvoicePurchaseHeader.Types.Entity;
					invoicePurchaseSpec: Types.Dal.DocInvoicePurchaseItem.Types.ListEntity[];
				}>
			> => {
				const { id } = payload;

				if (!transactionClient && !meta) {
					return this.#businessError.error.ACCESS_DENIED();
				}

				const entity = await this.#getEntity({ id });

				if (entity.error) return { error: entity.error };

				// Check for user are permitted (admin or warehouse manager)
				if (meta?.user) {
					const { error }
						= await this.services.warehouse.innerSpace.checkWarehouseOwner(
							meta,
							entity.data.warehouse_id,
						);

					if (error) {
						return {
							error,
						};
					}
				}

				if (entity.data.system_status !== Enum.SystemStatus.draft) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"ONLY_DRAFT_DOC_PERMITTED_TO_ACCEPT",
					);
				}

				{
					const { error } = this.#check.isCanBeUpdated(
						entity.data.system_status,
					);

					if (error) return { error };
				}

				const invoicePurchaseSpec
					= await this.services.docInvoicePurchaseItem.innerSpace.getSpecByHeaderId(
						{
							headerId: id,
							client: transactionClient,
						},
					);

				if (invoicePurchaseSpec.error) {
					return { error: invoicePurchaseSpec.error };
				}

				if (invoicePurchaseSpec.data.length === 0) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"NOT_POSSIBLE_TO_CHANGE_STATUS_FOR_EMPTY_DOCUMENT",
					);
				}

				if (
					invoicePurchaseSpec.data.some((specItem) =>
						Utils.Micros.isFirstGreaterOrEqual("0", specItem.quantity),
					)
				) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"ONLY_POSITIVE_QUANTITY_FOR_PURCHASE_SPEC_ITEM_ALLOWED_TO_ACCEPT",
					);
				}

				// Проверка отсутствия длящегося документа инвентаризации
				{
					const isInProgress
						= await this.services.docActInventoryHeader.commonCheck.isInProgressStatusExists(
							{
								warehouseId: entity.data.warehouse_id,
							},
						);

					if (isInProgress)
						return this.#businessError.error.UNKNOWN_ERROR(
							"IMPOSSIBLE_TO_ACCEPT_DOCS_DURING_IN_PROGRESS_INVENTORY",
						);
				}

				return {
					data: {
						entity: entity.data,
						invoicePurchaseSpec: invoicePurchaseSpec.data,
					},
				};
			},
			decline: async (
				payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Decline.Params,
				meta?: CommonTypes.Types.NatsProtocolApiMeta,
			): Promise<
				Types.Common.TDataError<Types.Dal.DocInvoicePurchaseHeader.Types.Entity>
			> => {
				const { id } = payload;

				if (!meta?.user) {
					return this.#businessError.error.ACCESS_DENIED();
				}

				const entity = await this.#getEntity({ id });

				if (entity.error) return { error: entity.error };

				// Check for user are permitted (admin or warehouse manager)
				{
					const { error }
						= await this.services.warehouse.innerSpace.checkWarehouseOwner(
							meta,
							entity.data.warehouse_id,
						);

					if (error) {
						return {
							error,
						};
					}
				}

				if (entity.data.system_status !== Enum.SystemStatus.accepted) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"ONLY_ACCEPTED_DOC_PERMITTED_TO_DECLINE",
					);
				}

				return entity;
			},
		},
		isCanBeUpdated: (systemStatus: number): Types.Common.TDataError<true> => {
			if (systemStatus !== Enum.SystemStatus.draft) {
				return this.#businessError.error.ACCESS_DENIED();
			}

			return { data: true };
		},
		isRelevanceDateCorrect: (relevanceDate: string): TDataError<boolean> => {
			const date = new Date(relevanceDate);

			if (date > new Date()) {
				return this.#businessError.error.UNKNOWN_ERROR(
					"FORBIDDEN_RELEVANCE_DATE_AT_THE_FUTURE",
				);
			}

			return {
				data: true,
			};
		},
	};

	async #getEntity(payload: {
		id: string;
	}): Promise<
		Types.Common.TDataError<Types.Dal.DocInvoicePurchaseHeader.Types.Entity>
	> {
		const entity = await this.#repository.getEntity(payload);

		if (!entity) {
			return this.#businessError.error.UNKNOWN_ERROR(
				"DOC_INVOICE_PURCHASE_HEADER_NOT_FOUND",
			);
		}

		return { data: entity };
	}

	async #getEntityForCheck(payload: {
		id?: string;
	}): Promise<
		Types.Common.TDataError<Types.Dal.DocInvoicePurchaseHeader.Types.EntityForCheck>
	> {
		const entity = await this.#repository.getEntityForCheck(payload);

		if (!entity) {
			return this.#businessError.error.UNKNOWN_ERROR(
				"DOC_INVOICE_PURCHASE_HEADER_FOR_CHECK_NOT_FOUND",
			);
		}

		return { data: entity };
	}

	async #accept(
		payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Accept.Params,
		meta?: CommonTypes.Types.NatsProtocolApiMeta,
		purposedAcceptorId?: string,
		transactionClient?: PoolClient,
	): Promise<
		Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Accept.Result>
	> {
		const { data: check, error } = await this.#check.before.accept(
			payload,
			meta,
			transactionClient,
		);

		if (error) return { error };

		try {
			const acceptorId = (meta?.user?.id ?? purposedAcceptorId) as string;

			const nomenclatureIds = Array.from(
				new Set(check.invoicePurchaseSpec.map((x) => x.nomenclature_item_id)),
			); // Только уникальные nomenclatureItemId

			const { data: nomenclatureItems }
				= await this.services.nomenclatureItem.innerSpace.getList({
					filters: {
						ids: nomenclatureIds,
					},
				});

			if (
				!nomenclatureItems?.list.length
				|| nomenclatureItems.list.length !== nomenclatureIds.length
			) {
				return this.#businessError.error.UNKNOWN_ERROR(
					"NOT_VALID_INVOICE_PURCHASE_SPEC",
				);
			}

			{
				const totalCostAmount = Utils.Micros.add(
					...check.invoicePurchaseSpec.map((specElement) =>
						Utils.Micros.mul(specElement.cost_price, specElement.quantity),
					),
				);

				const totalQuantity = Utils.Micros.add(
					...check.invoicePurchaseSpec.map(
						(specElement) => specElement.quantity,
					),
				);

				const positionCount = check.invoicePurchaseSpec.length;

				const { id } = await this.#transactions[
					"doc-invoice-purchase-header-accept"
				]({
					acceptorId,
					docInvoicePurchaseHeaderId: check.entity.id,
					positionCount,
					totalCostAmount,
					totalQuantity,
					companyId: check.entity.company_id,
					specItems: check.invoicePurchaseSpec,
					nomenclatureItems: nomenclatureItems.list,
					relatedEntityService: this.services.relatedEntity,
				});

				if (!id) {
					this.#logger.error(
						"In changeSystemStatus doc-invoice-purchase-header-accept not returning ID",
					);

					return this.#businessError.error.UNKNOWN_ERROR();
				}

				const resultEntity = await this.#getEntity({ id });

				if (resultEntity.error) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"NOT_POSSIBLE_TO_RECEIVE_ACCEPTED_DOCUMENT",
					);
				}

				return { data: Response.getEntity(resultEntity.data) };
			}
		} catch (e) {
			this.#logger.error("Error in changeSystemStatus: " + e);

			return this.#businessError.error.UNKNOWN_ERROR(String(e));
		}
	}

	async #decline(
		payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Decline.Params,
		meta?: CommonTypes.Types.NatsProtocolApiMeta,
	): Promise<
		Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Decline.Result>
	> {
		const { data: entity, error } = await this.#check.before.decline(
			payload,
			meta,
		);

		if (error) return { error };

		const resourceLockId = entity.warehouse_id;

		const { error: lockError } = await this.services.lockService.lockResource({
			resourceId: resourceLockId,
			resourceType: "warehouse",
		});

		if (lockError) {
			return {
				error: lockError,
			};
		}

		try {
			const { id } = await this.#transactions[
				"doc-invoice-purchase-header-decline"
			]({
				docInvoicePurchaseHeaderId: entity.id,
			});

			if (!id) {
				this.#logger.error(
					"In changeSystemStatus doc-invoice-purchase-header-decline not returning ID",
				);

				return this.#businessError.error.UNKNOWN_ERROR();
			}

			const resultEntity = await this.#getEntity({ id });

			if (resultEntity.error) {
				return this.#businessError.error.UNKNOWN_ERROR(
					"NOT_POSSIBLE_TO_RECEIVE_DECLINED_DOCUMENT",
				);
			}

			return { data: Response.getEntity(resultEntity.data) };
		} catch (e) {
			return this.#businessError.error.UNKNOWN_ERROR(String(e));
		} finally {
			const { error } = await this.services.lockService.unlockResource({
				resourceId: resourceLockId,
				resourceType: "warehouse",
			});

			if (error) {
				this.#logger.warn(
					"Error in doc-invoice-purchase decline unlock" + error.message,
				);
			}
		}
	}

	innerSpace = {
		accept: async (payload: {
			acceptorId: string;
			docInvoicePurchaseHeader: {
				id: string;
				companyId: string;
			};
			transactionClient?: PoolClient;
			basisDocument?: {
				docType: "whs004";
				docHeaderId: string;
			};
		}): Promise<TDataError<boolean>> => {
			const {
				acceptorId,
				basisDocument,
				docInvoicePurchaseHeader,
				transactionClient,
			} = payload;

			this.#check.before.accept;

			try {
				const invoicePurchaseSpec
					= await this.services.docInvoicePurchaseItem.innerSpace.getSpecByHeaderId(
						{
							headerId: docInvoicePurchaseHeader.id,
							client: transactionClient,
						},
					);

				if (invoicePurchaseSpec.error) {
					return { error: invoicePurchaseSpec.error };
				}

				const nomenclatureIds = Array.from(
					new Set(invoicePurchaseSpec.data.map((x) => x.nomenclature_item_id)),
				); // Только уникальные nomenclatureItemId

				const { data: nomenclatureItems }
					= await this.services.nomenclatureItem.innerSpace.getList({
						filters: {
							ids: nomenclatureIds,
						},
					});

				if (
					!nomenclatureItems?.list.length
					|| nomenclatureItems.list.length !== nomenclatureIds.length
				) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"NOT_VALID_INVOICE_PURCHASE_SPEC",
					);
				}

				{
					const totalCostAmount = Utils.Micros.add(
						...invoicePurchaseSpec.data.map((specElement) =>
							Utils.Micros.mul(specElement.cost_price, specElement.quantity),
						),
					);

					const totalQuantity = Utils.Micros.add(
						...invoicePurchaseSpec.data.map(
							(specElement) => specElement.quantity,
						),
					);

					const positionCount = invoicePurchaseSpec.data.length;

					const { id } = await this.#transactions[
						"doc-invoice-purchase-header-accept"
					]({
						acceptorId,
						docInvoicePurchaseHeaderId: docInvoicePurchaseHeader.id,
						positionCount,
						totalCostAmount,
						totalQuantity,
						companyId: docInvoicePurchaseHeader.companyId,
						specItems: invoicePurchaseSpec.data,
						nomenclatureItems: nomenclatureItems.list,
						relatedEntityService: this.services.relatedEntity,
						transactionClient,
						basisDocument,
					});

					if (!id) {
						this.#logger.error(
							"In changeSystemStatus doc-invoice-purchase-header-accept not returning ID",
						);

						return this.#businessError.error.UNKNOWN_ERROR();
					}

					return { data: true };
				}
			} catch (e) {
				this.#logger.error("Error in changeSystemStatus: " + e);

				return this.#businessError.error.UNKNOWN_ERROR(String(e));
			}
		},
		checkIsCanBeUpdated: async (payload: number) =>
			this.#check.isCanBeUpdated(payload),
		create: async (payload: {
			author_id: string;
			create: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Create.Params;
			transactionClient?: PoolClient;
		}): Promise<TDataError<{ id: string; }>> => {
			const { author_id, create, transactionClient } = payload;

			const data = await this.#transactions
				["doc-invoice-purchase-header-create"]({
					create: {
						author_id,
						total_cost_amount: "0",
						position_count: 0,
						company_id: create.companyId,
						relevance_date: create.relevanceDate,
						system_status: Enum.SystemStatus.draft,
						total_quantity: "0",
						warehouse_id: create.warehouseId,
					},
					textPrefix: this.#PREFIX,
					transactionClient,
				})
				.catch((e) => this.#logger.warn(e.message));

			if (!data) return this.#businessError.error.UNKNOWN_ERROR();

			return { data: { id: data.id } };
		},
		getEntity: async (payload: { id: string; }) => this.#getEntity(payload),
		getEntityForCheck: async (payload: { id?: string; }) =>
			this.#getEntityForCheck(payload),
	};

	outerSpace = {
		create: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Create.Params,
			meta?: CommonTypes.Types.NatsProtocolApiMeta,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Create.Result>
		> => {
			if (!meta?.user) return this.#businessError.error.ACCESS_DENIED();

			const check = await this.#check.before.create(payload, meta);

			if (check.error) return { error: check.error };

			const data = await this.#transactions
				["doc-invoice-purchase-header-create"]({
					create: {
						author_id: meta.user.id,
						total_cost_amount: "0",
						position_count: 0,
						company_id: payload.companyId,
						relevance_date: payload.relevanceDate,
						system_status: Enum.SystemStatus.draft,
						total_quantity: "0",
						warehouse_id: payload.warehouseId,
					},
					textPrefix: this.#PREFIX,
				})
				.catch((e) => this.#logger.warn(e.message));

			if (!data) return this.#businessError.error.UNKNOWN_ERROR();

			const entity = await this.#getEntity({ id: data.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},
		getEntity: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.GetEntity.Params,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.GetEntity.Result>
		> => {
			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},
		getList: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.GetList.Params,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.GetList.Result>
		> => {
			const { filters, limit, offset, order } = payload;

			const [list, total] = await this.#repository.getList({
				order,
				pagination: { limit, offset },
				params: {
					...filters,
					systemStatuses: filters.systemStatuses && {
						$in: filters.systemStatuses.$in?.map(
							(status) => Enum.SystemStatus[status],
						),
						$nin: filters.systemStatuses.$nin?.map(
							(status) => Enum.SystemStatus[status],
						),
					},
				},
			});

			return {
				data: { list: list.map(Response.getListEntity), total },
			};
		},
		getPreparedList: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.GetPreparedList.Params,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.GetPreparedList.Result>
		> => {
			const { filters, limit, offset, order } = payload;

			const [list, total] = await this.#repository.getList({
				order,
				pagination: { limit, offset },
				params: {
					...filters,
					systemStatuses: filters.systemStatuses && {
						$in: filters.systemStatuses.$in?.map(
							(status) => Enum.SystemStatus[status],
						),
						$nin: filters.systemStatuses.$nin?.map(
							(status) => Enum.SystemStatus[status],
						),
					},
				},
			});

			return {
				data: {
					list: list.map(Response.getPreparedListEntity),
					total,
				},
			};
		},
		update: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Update.Params,
			meta?: CommonTypes.Types.NatsProtocolApiMeta,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Update.Result>
		> => {
			const check = await this.#check.before.update(payload, meta);

			if (check.error) return { error: check.error };

			await this.#repository.updateOneByPk(payload.id, {
				company_id: payload.companyId,
				relevance_date: payload.relevanceDate,
				warehouse_id: payload.warehouseId,
			});

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},
		accept: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Accept.Params,
			meta?: CommonTypes.Types.NatsProtocolApiMeta,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Accept.Result>
		> => {
			const { error } = await this.#check.before.accept(payload, meta);

			if (error) return { error };

			const queueResult = await this.services.queueManager.queues[
				"common-doc-header-operations"
			].exec<ReturnType<typeof this.queueHandlers.accept>>({
				meta,
				method: "accept",
				payload,
				type: "doc-invoice-purchase-header",
			});

			if (queueResult.error) {
				return {
					error: queueResult.error,
				};
			}

			return { data: queueResult.data };
		},
		decline: async (
			payload: NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Decline.Params,
			meta?: CommonTypes.Types.NatsProtocolApiMeta,
		): Promise<
			Types.Common.TDataError<NatsProtocol.Domains.WarehouseDomain.DocInvoicePurchaseHeader.Decline.Result>
		> => {
			const { error } = await this.#check.before.decline(payload, meta);

			if (error) return { error };

			const queueResult = await this.services.queueManager.queues[
				"common-doc-header-operations"
			].exec<ReturnType<typeof this.queueHandlers.decline>>({
				meta,
				method: "decline",
				payload,
				type: "doc-invoice-purchase-header",
			});

			if (queueResult.error) {
				return {
					error: queueResult.error,
				};
			}

			return { data: queueResult.data };
		},
	};

	queueHandlers = {
		accept: this.#accept.bind(this),
		decline: this.#decline.bind(this),
	};
}
