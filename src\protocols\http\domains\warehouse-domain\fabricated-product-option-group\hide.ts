import { Static } from "@sinclair/typebox";

import { FabricatedProductOptionGroup } from "../../../../../dto/http/warehouse-domain";
import BaseMethod from "../../../base-http-method";

export const method = new BaseMethod({
	name: "warehouse-domain/fabricated-product-option-group/hide",
	params: FabricatedProductOptionGroup.Params.hide,
	result: FabricatedProductOptionGroup.Result.entity,
	systemPermissions: { admin: true, employee: true, guest: false },
	permissionDescription: "Скрытие группы опций",
});

export type Params = Static<typeof method.params>;
export type Result = Static<typeof method.result>;
