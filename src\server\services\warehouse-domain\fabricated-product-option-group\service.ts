import { FabricatedProductOptionGroup } from "@2people-it/inwave-erp-types/dist/protocols/http/domains/warehouse-domain/index.js";

import * as Types from "../../../types/index.js";

import BaseService from "../../base-service.js";

export default class Service extends BaseService {
	#broker;
	#logger;

	constructor(data: {
		broker: Types.Broker.default;
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#broker = data.broker;
		this.#logger = data.logger;
	}

	outerSpace = {
		create: async (
			payload: FabricatedProductOptionGroup.Create.Params,
			meta?: Types.Common.TUserMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOptionGroup.Create.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option-group/create",
				params: payload,
				meta,
			});

			return { data: result };
		},

		getEntity: async (
			payload: FabricatedProductOptionGroup.GetEntity.Params,
			meta?: Types.Common.TUserMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOptionGroup.GetEntity.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option-group/get-entity",
				params: payload,
				meta,
			});

			return { data: result };
		},

		getOptions: async (
			payload: FabricatedProductOptionGroup.GetOptions.Params,
			meta?: Types.Common.TUserMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOptionGroup.GetOptions.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option-group/get-options",
				params: payload,
				meta,
			});

			return { data: result };
		},

		getPreparedList: async (
			payload: FabricatedProductOptionGroup.GetPreparedList.Params,
			meta?: Types.Common.TUserMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOptionGroup.GetPreparedList.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option-group/get-prepared-list",
				params: payload,
				meta,
			});

			return { data: result };
		},

		update: async (
			payload: FabricatedProductOptionGroup.Update.Params,
			meta?: Types.Common.TUserMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOptionGroup.Update.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option-group/update",
				params: payload,
				meta,
			});

			return { data: result };
		},

		hide: async (
			payload: FabricatedProductOptionGroup.Hide.Params,
			meta?: Types.Common.TUserMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOptionGroup.Hide.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option-group/hide",
				params: payload,
				meta,
			});

			return { data: result };
		},

		unhide: async (
			payload: FabricatedProductOptionGroup.Unhide.Params,
			meta?: Types.Common.TUserMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOptionGroup.Unhide.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option-group/unhide",
				params: payload,
				meta,
			});

			return { data: result };
		},

		remove: async (
			payload: FabricatedProductOptionGroup.Remove.Params,
			meta?: Types.Common.TUserMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOptionGroup.Remove.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option-group/remove",
				params: payload,
				meta,
			});

			return { data: result };
		},
	};
}
