import { Common, NatsProtocol } from "@2people-it/inwave-erp-types";

import * as Types from "../../types/index.js";
import BaseService from "../base-service.js";

export default class Service extends BaseService {
	#bullmq;
	#config;
	#logger;

	#queues;

	constructor(data: {
		bullmq: Types.System.BullMQ.Service;
		config: Types.Config.ConfigOptions;
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#bullmq = data.bullmq;
		this.#config = data.config;
		this.#logger = data.logger;

		this.#queues = {
			"common-doc-header-operations":
				this.#init["common-doc-header-operations"].queue(),
			"fabricated-product-option-operations":
				this.#init["fabricated-product-option-operations"].queue(),
			"option-adjust-header-operations":
				this.#init["option-adjust-header-operations"].queue(),
			"fabricated-product-option-group-operations":
				this.#init["fabricated-product-option-group-operations"].queue(),
			"doc-bom-header-operations":
				this.#init["doc-bom-header-operations"].queue(),
			"serial-number-operations":
				this.#init["serial-number-operations"].queue(),
		};
	}

	#initQueue(queueName: string) {
		const [queue, queueEvents] = this.#bullmq.createQueueData(queueName);

		if (this.#config.IS_MAIN_THREAD) {
			queueEvents.on("waiting", ({ jobId }) => {
				this.#logger.info(`${queueName}. Job ${jobId} is waiting`);
			});

			queueEvents.on("active", ({ jobId, prev }) => {
				this.#logger.info(
					`${queueName}. Job ${jobId} is now active; previous status was ${prev}`,
				);
			});

			queueEvents.on("completed", ({ jobId }) => {
				this.#logger.info(`${queueName}. Job ${jobId} has completed`);
			});

			queueEvents.on("failed", ({ failedReason, jobId }) => {
				this.#logger.error(
					`${queueName}. Job ${jobId} has failed with reason ${failedReason}`,
				);
			});
		}

		return [queue, queueEvents] as const;
	}

	#createQueueWorker(
		queueName: string,
		cb: (job: Types.System.BullMQ.Job) => Promise<unknown>,
	) {
		if (!this.#config.IS_MAIN_THREAD) return;

		const [Worker, connection] = this.#bullmq.getWorker();

		const worker = new Worker(queueName, cb, {
			concurrency: 1,
			connection,
			lockDuration: 5 * 60_000,
			removeOnComplete: { count: 1000 },
			removeOnFail: { count: 5000 },
		});

		worker.on("failed", (job, error) => {
			this.#logger.error(
				`${queueName}. Job ${job?.id}. Worker was failed with error: ${error.message}`,
			);
		});

		worker.on("error", (error) => {
			this.#logger.error(
				`${queueName}. Worker was failed with error: ${error.message}`,
			);
		});

		return worker;
	}

	#init = {
		"common-doc-header-operations": {
			queue: () => {
				const queueName = "WD__COMMON-DOC-HEADER-OPERATIONS";
				const [queue, queueEvents] = this.#initQueue(queueName);

				const worker = this.#createQueueWorker(queueName, (job) => {
					const jobData = job.data as CommonOperationJobData;

					try {
						switch (jobData.type) {
							case "doc-invoice-purchase-header": {
								if (
									jobData.method
									in this.services.docInvoicePurchaseHeader.queueHandlers
								) {
									return this.services.docInvoicePurchaseHeader.queueHandlers[
										jobData.method
									](jobData.payload, jobData.meta);
								} else {
									throw new Error(
										"Method for doc-invoice-purchase-header "
											+ jobData.method
											+ " not implemented yet",
									);
								}
							}

							case "doc-invoice-sale-header": {
								if (
									jobData.method
									in this.services.docInvoiceSaleHeader.queueHandlers
								) {
									return this.services.docInvoiceSaleHeader.queueHandlers[
										jobData.method
									](jobData.payload, jobData.meta);
								} else {
									throw new Error(
										"Method for doc-invoice-sale-header "
											+ jobData.method
											+ " not implemented yet",
									);
								}
							}

							case "doc-internal-movement-header": {
								if (
									jobData.method
									in this.services.docInternalMovementHeader.queueHandlers
								) {
									const meta: Common.Types.NatsProtocolApiMeta
										= jobData.meta || {
											microservice: {
												hostName: this.#config.SYSTEM_HOSTNAME,
												ipAddress: this.#config.SYSTEM_IP_ADDRESS,
												name: NatsProtocol.Domains.WarehouseDomain.domainName,
											},
										};

									return this.services.docInternalMovementHeader.queueHandlers[
										jobData.method
									](jobData.payload, meta);
								} else {
									throw new Error(
										"Method for doc-internal-movement-header "
											+ jobData.method
											+ " not implemented yet",
									);
								}
							}

							case "doc-act-fabrication-header": {
								if (
									jobData.method
									in this.services.docActFabricationHeader.queueHandlers
								) {
									const meta: Common.Types.NatsProtocolApiMeta
										= jobData.meta || {
											microservice: {
												hostName: this.#config.SYSTEM_HOSTNAME,
												ipAddress: this.#config.SYSTEM_IP_ADDRESS,
												name: NatsProtocol.Domains.WarehouseDomain.domainName,
											},
										};

									return this.services.docActFabricationHeader.queueHandlers[
										jobData.method
									](jobData.payload, meta);
								} else {
									throw new Error(
										"Method for doc-act-fabrication-header "
											+ jobData.method
											+ " not implemented yet",
									);
								}
							}

							case "doc-act-inventory-header": {
								if (
									jobData.method
									in this.services.docActInventoryHeader.queueHandlers
								) {
									return this.services.docActInventoryHeader.queueHandlers[
										jobData.method
									](jobData.payload, jobData.meta);
								} else {
									throw new Error(
										"Method for doc-act-inventory-header "
											+ jobData.method
											+ " not implemented yet",
									);
								}
							}

							default: {
								throw new Error(
									"Not available handler for: " + JSON.stringify(jobData),
								);
							}
						}
					} catch (error) {
						throw error;
					}
				});

				if (!worker) throw new Error("Queue worker is not initialized");

				return [queue, queueEvents, worker] as const;
			},
		},

		"fabricated-product-option-operations": {
			queue: () => {
				const queueName = "WD__FABRICATED-PRODUCT-OPTION-OPERATIONS";
				const [queue, queueEvents] = this.#initQueue(queueName);

				const worker = this.#createQueueWorker(queueName, (job) => {
					if (
						job.data.type
						in this.services.fabricatedProductOption.queueJobHandlers
					) {
						return this.services.fabricatedProductOption.queueJobHandlers[
							job.data
								.type as keyof typeof this.services.fabricatedProductOption.queueJobHandlers
						](job.data.payload);
					}

					throw new Error("Not available");
				});

				if (!worker) throw new Error("Queue worker is not initialized");

				return [queue, queueEvents, worker] as const;
			},
		},

		"fabricated-product-option-group-operations": {
			queue: () => {
				const queueName = "WD__FABRICATED-PRODUCT-OPTION-GROUP-OPERATIONS";
				const [queue, queueEvents] = this.#initQueue(queueName);

				const worker = this.#createQueueWorker(queueName, (job) => {
					if (
						job.data.type
						in this.services.fabricatedProductOptionGroup.queueJobHandlers
					) {
						return this.services.fabricatedProductOptionGroup.queueJobHandlers[
							job.data
								.type as keyof typeof this.services.fabricatedProductOptionGroup.queueJobHandlers
						](job.data.payload);
					}

					throw new Error("Not available");
				});

				if (!worker) throw new Error("Queue worker is not initialized");

				return [queue, queueEvents, worker] as const;
			},
		},

		"option-adjust-header-operations": {
			queue: () => {
				const queueName = "WD__OPTION-ADJUST-HEADER-OPERATIONS";
				const [queue, queueEvents] = this.#initQueue(queueName);

				const worker = this.#createQueueWorker(queueName, (job) => {
					if (
						job.data.method in this.services.optionAdjustHeader.queueJobHandlers
					) {
						return this.services.optionAdjustHeader.queueJobHandlers[
							job.data
								.method as keyof typeof this.services.optionAdjustHeader.queueJobHandlers
						](job.data.payload);
					}

					throw new Error("Not available.");
				});

				if (!worker) throw new Error("Queue worker is not initialized.");

				return [queue, queueEvents, worker] as const;
			},
		},

		"doc-bom-header-operations": {
			queue: () => {
				const queueName = "WD__DOC-BOM-HEADER-OPERATIONS";
				const [queue, queueEvents] = this.#initQueue(queueName);

				const worker = this.#createQueueWorker(queueName, (job) => {
					if (
						job.data.type
						in this.services.docBillOfMaterialHeader.queueJobHandlers
					) {
						return this.services.docBillOfMaterialHeader.queueJobHandlers[
							job.data
								.type as keyof typeof this.services.docBillOfMaterialHeader.queueJobHandlers
						](job.data.payload, job.data.meta);
					}

					throw new Error("Not available");
				});

				if (!worker) throw new Error("Queue worker is not initialized");

				return [queue, queueEvents, worker] as const;
			},
		},

		"serial-number-operations": {
			queue: () => {
				const queueName = "WD__SERIAL-NUMBER-OPERATIONS";
				const [queue, queueEvents] = this.#initQueue(queueName);

				const worker = this.#createQueueWorker(queueName, (job) => {
					if (job.data.type in this.services.serialNumber.queueJobHandlers) {
						return this.services.serialNumber.queueJobHandlers[
							job.data
								.type as keyof typeof this.services.serialNumber.queueJobHandlers
						](job.data.payload);
					}

					throw new Error("Not available");
				});

				if (!worker) throw new Error("Queue worker is not initialized");

				return [queue, queueEvents, worker] as const;
			},
		},
	};

	queues = {
		// Очередь для операций с заголовками всех документов, влияющих на складской остаток,
		"common-doc-header-operations": {
			exec: async <R>(data: CommonOperationJobData): Promise<R> => {
				const jobName = "common-doc-header-operations";
				const [queue, queueEvents] = this.#queues[jobName];

				const job = await queue.add(jobName, data, { attempts: 1 });

				return job.waitUntilFinished(queueEvents);
			},
		},

		"fabricated-product-option-operations": {
			exec: async (
				data:
					| {
						type: "create";
						payload: NatsProtocol.Domains.WarehouseDomain.FabricatedProductOption.Create.Params;
						meta?: Common.Types.NatsProtocolApiMeta;
					  }
					| {
						type: "update";
						payload: NatsProtocol.Domains.WarehouseDomain.FabricatedProductOption.Update.Params;
						meta?: Common.Types.NatsProtocolApiMeta;
					  }
					| {
						type: "hide";
						payload: NatsProtocol.Domains.WarehouseDomain.FabricatedProductOption.Hide.Params;
						meta?: Common.Types.NatsProtocolApiMeta;
					  }
					| {
						type: "unhide";
						payload: NatsProtocol.Domains.WarehouseDomain.FabricatedProductOption.Unhide.Params;
						meta?: Common.Types.NatsProtocolApiMeta;
					  },
			): Promise<Common.Types.TDataError<{ id: string; }>> => {
				const jobName = "fabricated-product-option-operations";
				const [queue, queueEvents] = this.#queues[jobName];

				const job = await queue.add(jobName, data, { attempts: 1 });

				return job.waitUntilFinished(queueEvents);
			},
		},

		"fabricated-product-option-group-operations": {
			exec: async (
				data:
					| {
						type: "create";
						payload: NatsProtocol.Domains.WarehouseDomain.FabricatedProductOptionGroup.Create.Params;
						meta?: Common.Types.NatsProtocolApiMeta;
					  }
					| {
						type: "update";
						payload: NatsProtocol.Domains.WarehouseDomain.FabricatedProductOptionGroup.Update.Params;
						meta?: Common.Types.NatsProtocolApiMeta;
					  }
					| {
						type: "hide";
						payload: NatsProtocol.Domains.WarehouseDomain.FabricatedProductOptionGroup.Hide.Params;
						meta?: Common.Types.NatsProtocolApiMeta;
					  }
					| {
						type: "unhide";
						payload: NatsProtocol.Domains.WarehouseDomain.FabricatedProductOptionGroup.Unhide.Params;
						meta?: Common.Types.NatsProtocolApiMeta;
					  },
			): Promise<Common.Types.TDataError<{ id: string; }>> => {
				const jobName = "fabricated-product-option-group-operations";
				const [queue, queueEvents] = this.#queues[jobName];

				const job = await queue.add(jobName, data, { attempts: 1 });

				return job.waitUntilFinished(queueEvents);
			},
		},

		"option-adjust-header-operations": new Proxy(
			{} as Partial<typeof this.services.optionAdjustHeader.queueJobHandlers>,
			{
				get: (target, property) => {
					const methodName = String(property);

					if (Object.hasOwn(target, methodName)) {
						return target[methodName as keyof typeof target];
					}

					if (methodName in this.services.optionAdjustHeader.queueJobHandlers) {
						const handler = async (payload: unknown, meta?: unknown) => {
							const jobName = "option-adjust-header-operations";
							const [queue, queueEvents] = this.#queues[jobName];

							const job = await queue.add(
								jobName,
								{
									method: methodName,
									payload,
									meta,
								},
								{ attempts: 1 },
							);

							return job.waitUntilFinished(queueEvents);
						};

						target[methodName as keyof typeof target] = handler;

						return handler;
					}

					throw new Error(
						`No method '${methodName}' in option adjust header job handlers.`,
					);
				},
			},
		) as typeof this.services.optionAdjustHeader.queueJobHandlers,

		// Очередь для операций с заголовками BOM
		"doc-bom-header-operations": {
			exec: async (
				data:
					| {
						type: "copy";
						payload: NatsProtocol.Domains.WarehouseDomain.DocBillOfMaterialHeader.Copy.Params;
						meta: Common.Types.NatsProtocolApiMeta | undefined;
					  }
					| {
						type: "create";
						payload: NatsProtocol.Domains.WarehouseDomain.DocBillOfMaterialHeader.Create.Params;
						meta: Common.Types.NatsProtocolApiMeta | undefined;
					  },
			): Promise<Types.Common.TDataError<{ id: string; }>> => {
				const jobName = "doc-bom-header-operations";
				const [queue, queueEvents] = this.#queues[jobName];

				const job = await queue.add(jobName, data, { attempts: 1 });

				return job.waitUntilFinished(queueEvents);
			},
		},

		"serial-number-operations": {
			exec: async (data: {
				type: "create";
				payload: {
					fabricatedProductId: string;
					prefix: string;
					increment?: number | undefined;
				};
			}): Promise<Types.Common.TDataError<{ id: string; }>> => {
				const jobName = "serial-number-operations";
				const [queue, queueEvents] = this.#queues[jobName];

				const job = await queue.add(jobName, data, { attempts: 1 });

				return job.waitUntilFinished(queueEvents);
			},
		},
	};

	async shutdown(): Promise<void> {
		for (const [queue, queueEvents, worker] of Object.values(this.#queues)) {
			await Promise.all([queue.close(), queueEvents.close(), worker.close()]);
		}
	}

	async flushQueue() {
		for (const [queue] of Object.values(this.#queues)) {
			await queue.obliterate({ force: true });
			this.#logger.info("flushQueue done for queue " + queue.name);
		}
	}
}

export type CommonOperationJobData =
	| {
		type: "doc-invoice-purchase-header"; // Приходные накладные
		meta?: Common.Types.NatsProtocolApiMeta;
		method: "accept" | "decline";
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		payload: any;
	  }
	| {
		type: "doc-invoice-sale-header"; // Расходные накладные
		meta?: Common.Types.NatsProtocolApiMeta;
		method: "accept" | "decline";
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		payload: any;
	  }
	| {
		type: "doc-internal-movement-header"; // Внутренние перемещения
		meta?: Common.Types.NatsProtocolApiMeta;
		method: "reserve" | "forward" | "take" | "decline";
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		payload: any;
	  }
	| {
		type: "doc-act-fabrication-header"; // Акт производства
		meta?: Common.Types.NatsProtocolApiMeta;
		method: "accept" | "update";
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		payload: any;
	  }
	| {
		type: "doc-act-inventory-header"; // Акт инвентаризации
		meta?: Common.Types.NatsProtocolApiMeta;
		method: "accept" | "decline" | "start";
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		payload: any;
	  };
