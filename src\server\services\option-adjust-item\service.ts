import InwaveTypes from "@2people-it/inwave-erp-types";
import { WarehouseDomain } from "@2people-it/inwave-erp-types/dist/protocols/nats/domains/index.js";

import * as Response from "./response.js";
import * as Types from "../../types/index.js";
import BaseService from "../base-service.js";
import { SystemStatus } from "../doc-bill-of-material-header/enum/index.js";

const versionSystemStatus
	= InwaveTypes.BaseEntities.WarehouseDomain.OptionAdjustHeader.systemStatus;

const specTypeEnum
	= InwaveTypes.BaseEntities.WarehouseDomain.OptionAdjustItem.specType;

const sourceType
	= InwaveTypes.BaseEntities.WarehouseDomain.OptionAdjustItem.sourceType;

export default class Service extends BaseService {
	#businessError;
	#logger;
	#transactions;
	#repository;

	constructor(data: {
		businessError: Types.System.BusinessError.Service;
		dal: Types.ServiceLocator.default["dal"];
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#businessError = data.businessError;
		this.#logger = data.logger;
		this.#transactions = data.dal.transactions;
		this.#repository = data.dal.repository.optionAdjustItem;
	}

	#check = {
		before: {
			getSpec: async (
				payload: WarehouseDomain.OptionAdjustItem.GetSpec.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { headerId } = payload;

				// check for header exists
				{
					const { error }
						= await this.services.optionAdjustHeader.innerSpace.getEntityForCheck(
							{
								id: headerId,
							},
						);

					if (error) return { error };
				}

				return { data: true };
			},

			saveSpec: async (
				payload: WarehouseDomain.OptionAdjustItem.SaveSpec.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { headerId, items } = payload;

				const header
					= await this.services.optionAdjustHeader.innerSpace.getEntity({
						id: headerId,
					});

				if (header.error) return { error: header.error };

				if (header.data.system_status !== versionSystemStatus.draft) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"HEADER_MUST_BE_DRAFT",
					);
				}

				const documentationNomenclatureItemIdSet = new Set<string>();
				const nomenclatureItemIdSet = new Set<string>();
				const warehouseIdSet = new Set<string>();
				const docBomHeaderIdSetToBeAccepted = new Set<string>();
				const headerIdSet = new Set<string>();

				for (const item of items) {
					if (item.nomenclatureItemId) {
						// Номенклатурные позиции в разделе Документация должны быть уникальными
						if (
							item.section
							=== InwaveTypes.Common.specItemSection.constEnum["documentation"]
						) {
							if (
								documentationNomenclatureItemIdSet.has(item.nomenclatureItemId)
							) {
								return this.#businessError.error.UNKNOWN_ERROR(
									"DOCUMENTATION_NOMENCLATURE_ITEMS_MUST_BE_UNIQUE",
								);
							}

							documentationNomenclatureItemIdSet.add(item.nomenclatureItemId);
						}

						const { data: nomenclatureItem, error }
							= await this.services.nomenclatureItem.innerSpace.getEntityForCheck(
								{
									id: item.nomenclatureItemId,
								},
							);

						if (error) return { error };

						if (nomenclatureItem.is_fabricable_product) {
							// Если номенклатурная позиция в строке указана и имеет тип “Изделие”,
							// то для неё может быть выбрана версия её спецификации
							// Выбрана может быть только согласованная версия спецификации.
							if (item.docBillOfMaterialHeaderId) {
								docBomHeaderIdSetToBeAccepted.add(
									item.docBillOfMaterialHeaderId,
								);
							}

							if (item.options?.length) {
								const optionIds = [...new Set(item.options.map((e) => e.id))];

								const options
									= await this.services.fabricatedProductOption.innerSpace.getEntitiesForCheck(
										{
											ids: optionIds,
										},
									);

								if (options.length < optionIds.length) {
									return this.#businessError.error.ENTITY_NOT_FOUND(
										"NOT_FOUND_OPTION",
									);
								}

								const { error }
									= await this.services.fabricatedProductOption.innerSpace.checkIsForNomenclatureItem(
										{
											ids: optionIds,
											nomenclatureItemId: item.nomenclatureItemId,
										},
									);

								if (error) return { error };
							}
						} else {
							if (item.options?.length) {
								return this.#businessError.error.UNKNOWN_ERROR(
									"MATERIAL_CANNOT_HAVE_OPTIONS",
								);
							}
						}

						nomenclatureItemIdSet.add(item.nomenclatureItemId);
					}

					if (
						(item.source?.type === "warehouse"
							|| item.source?.type === "fabrication")
						&& item.source.warehouseId
					) {
						warehouseIdSet.add(item.source.warehouseId);
					}
				}

				if (nomenclatureItemIdSet.size) {
					const nomenclatureItemIds = [...nomenclatureItemIdSet];

					const entities
						= await this.services.nomenclatureItem.innerSpace.getEntitiesForCheck(
							{
								ids: nomenclatureItemIds,
							},
						);

					if (entities.length < nomenclatureItemIds.length) {
						return this.#businessError.error.NOMENCLATURE_ITEM_NOT_FOUND();
					}
				}

				if (warehouseIdSet.size) {
					const warehouseIds = [...warehouseIdSet];

					const { error }
						= await this.services.warehouse.innerSpace.getEntitiesForCheck({
							ids: warehouseIds,
						});

					if (error) return { error };
				}

				if (docBomHeaderIdSetToBeAccepted.size) {
					const docBomHeaderIds = [...docBomHeaderIdSetToBeAccepted];

					const headers
						= await this.services.docBillOfMaterialHeader.innerSpace.getEntitiesForCheck(
							{
								ids: docBomHeaderIds,
							},
						);

					if (headers.length < docBomHeaderIds.length) {
						return this.#businessError.error.ENTITY_NOT_FOUND(
							"NOT_FOUND_DOC_BILL_OF_MATERIAL_HEADER",
						);
					}

					if (headers.some((e) => e.system_status !== SystemStatus.accepted)) {
						return this.#businessError.error.ENTITY_NOT_FOUND(
							"DOC_BILL_OF_MATERIAL_HEADER_MUST_BE_ACCEPTED",
						);
					}
				}

				{
					const { error } = await this.#check.isNotRecursiveDependent({
						nomenclatureItemId: header.data.option_id,
						specNomenclatureItemIds: [...nomenclatureItemIdSet],
						headerIds: [...headerIdSet],
					});

					if (error) return { error };
				}

				return { data: true };
			},
		},

		isCanBeAccepted: async (
			item: Types.Dal.OptionAdjustItem.Types.Entity,
		): Promise<
			Types.Common.TDataError<{
				docBillOfMaterialHeaderIds: string[];
			}>
		> => {
			if (item.quantity === null) {
				return this.#businessError.error.UNKNOWN_ERROR("QUANTITY_MUST_BE_SET");
			}

			if (item.nomenclature_item_id === null) {
				return this.#businessError.error.UNKNOWN_ERROR(
					"NOMENCLATURE_ITEM_MUST_BE_SET",
				);
			}

			const nomenclatureItem
				= await this.services.nomenclatureItem.innerSpace.getEntityForCheck({
					id: item.nomenclature_item_id,
				});

			if (nomenclatureItem.error) return { error: nomenclatureItem.error };

			if (nomenclatureItem.data.is_fabricable_product) {
				// для номенклатуры с типом “Изделие” могут быть указаны только целые числа
				if (!InwaveTypes.Utils.Micros.isInteger(item.quantity)) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"FABRICABLE_PRODUCT_QUANTITY_MUST_BE_INTEGER",
					);
				}
			}

			const docBillOfMaterialHeaderIds: string[] = [];

			if (item.doc_bill_of_material_header_id) {
				docBillOfMaterialHeaderIds.push(item.doc_bill_of_material_header_id);
			}

			return {
				data: {
					docBillOfMaterialHeaderIds,
				},
			};
		},

		isNotRecursiveDependent: async (payload: {
			nomenclatureItemId: string;
			specNomenclatureItemIds: string[];
			headerIds: string[];
		}): Promise<Types.Common.TDataError<true>> => {
			let headerIds = [...payload.headerIds];

			if (
				payload.specNomenclatureItemIds.includes(payload.nomenclatureItemId)
			) {
				return this.#businessError.error.UNKNOWN_ERROR("RECURSION_NOT_ALLOWED");
			}

			while (headerIds.length > 0) {
				const items = await this.#repository.getArrByParams({
					params: {
						header_id: { $in: headerIds },
					},
				});

				headerIds = [];

				for (const item of items) {
					if (item.nomenclature_item_id === payload.nomenclatureItemId) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"RECURSION_NOT_ALLOWED",
						);
					}
				}
			}

			return { data: true };
		},
	};

	innerSpace = {
		getSpec: async (
			payload: WarehouseDomain.OptionAdjustItem.GetSpec.Params,
		): Promise<
			Types.Common.TDataError<Types.Dal.OptionAdjustItem.Types.Entity[]>
		> => {
			const check = await this.#check.before.getSpec(payload);

			if (check.error) return { error: check.error };

			const spec = await this.#repository.getSpec({
				headerId: payload.headerId,
				specType: specTypeEnum.getByKey(payload.specType),
			});

			return { data: spec };
		},

		checkIsCanBeAccepted: this.#check.isCanBeAccepted.bind(this),
	};

	outerSpace = {
		getSpec: async (
			payload: WarehouseDomain.OptionAdjustItem.GetSpec.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustItem.GetSpec.Result>
		> => {
			const spec = await this.innerSpace.getSpec(payload);

			if (spec.error) return { error: spec.error };

			return { data: spec.data.map(Response.getSpecEntity) };
		},

		saveSpec: async (
			payload: WarehouseDomain.OptionAdjustItem.SaveSpec.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.OptionAdjustItem.SaveSpec.Result>
		> => {
			const check = await this.#check.before.saveSpec(payload);

			if (check.error) return { error: check.error };

			await this.#transactions["option-adjust-items-save"]({
				headerId: payload.headerId,
				specType: specTypeEnum.getByKey(payload.specType),
				items: payload.items.map((item) => ({
					section: InwaveTypes.Common.specItemSection.getByKey(item.section),
					nomenclatureItemId: item.nomenclatureItemId,
					docBillOfMaterialHeaderId: item.docBillOfMaterialHeaderId,
					quantity: item.quantity,
					source: item.source
						? {
							warehouseId: null,
							...item.source,
							type: sourceType.getByKey(item.source.type),
						  }
						: null,
					options: item.options ?? [],
					eskdTitle: item.eskdTitle,
					nonFormalizedTitle: item.nonFormalizedTitle,
					notice: item.notice,
				})),
			});

			return { data: true };
		},
	};
}
