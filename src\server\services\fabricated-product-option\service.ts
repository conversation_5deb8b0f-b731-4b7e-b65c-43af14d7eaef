import InwaveTypes from "@2people-it/inwave-erp-types";
import { WarehouseDomain } from "@2people-it/inwave-erp-types/dist/protocols/nats/domains/index.js";

import * as Response from "./response.js";
import * as Types from "../../types/index.js";
import BaseService from "../base-service.js";

const optionAdjustHeaderSystemStatus
	= InwaveTypes.BaseEntities.WarehouseDomain.OptionAdjustHeader.systemStatus;

export default class Service extends BaseService {
	#businessError;
	#logger;
	#repository;
	#transactions;

	constructor(data: {
		businessError: Types.System.BusinessError.Service;
		dal: Types.ServiceLocator.default["dal"];
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#businessError = data.businessError;
		this.#logger = data.logger;
		this.#repository = data.dal.repository.fabricatedProductOption;
		this.#transactions = data.dal.transactions;
	}

	#check = {
		before: {
			create: async (
				payload: WarehouseDomain.FabricatedProductOption.Create.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { groupId, incompatibleOptionIds, requiredOptionIds } = payload;

				// check for group exists
				const { data, error: entityError }
					= await this.services.fabricatedProductOptionGroup.innerSpace.getEntityForCheck(
						{
							id: groupId,
						},
					);

				if (entityError) return { error: entityError };

				const optionGroupIds = new Set<string>();

				// check for incompatible options exist
				{
					const [validOptions] = await this.innerSpace.getEntities({
						ids: incompatibleOptionIds,
					});

					if (validOptions.length !== incompatibleOptionIds.length) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"INVALID_INCOMPATIBLE_OPTIONS",
						);
					}

					validOptions.forEach((option) => {
						optionGroupIds.add(option.group_id);
					});
				}

				// check for required options exist
				{
					const [validOptions] = await this.innerSpace.getEntities({
						ids: requiredOptionIds,
					});

					if (validOptions.length !== requiredOptionIds.length) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"INVALID_REQUIRED_OPTIONS",
						);
					}

					validOptions.forEach((option) => {
						optionGroupIds.add(option.group_id);
					});
				}

				// Проверим, что все optionGroupIds принадлежат позиции
				if (optionGroupIds.size) {
					const groups
						= await this.services.fabricatedProductOptionGroup.innerSpace.getEntitiesForCheck(
							{
								ids: Array.from(optionGroupIds),
							},
						);

					if (
						groups.some(
							(group) =>
								group.nomenclature_item_id !== data.nomenclature_item_id,
						)
					) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"ONLY_SELF_OPTIONS_CAN_BE_CONTROLLED",
						);
					}
				}

				return { data: true };
			},

			update: async (
				payload: WarehouseDomain.FabricatedProductOption.Update.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { id, incompatibleOptionIds, requiredOptionIds } = payload;

				const { data, error } = await this.#getEntityForCheck({ id });

				if (error) return { error };

				// check for group exists
				const { data: groupData, error: groupError }
					= await this.services.fabricatedProductOptionGroup.innerSpace.getEntityForCheck(
						{
							id: data.group_id,
						},
					);

				if (groupError) return { error: groupError };

				const optionGroupIds = new Set<string>();

				// check for incompatible options exist
				if (incompatibleOptionIds) {
					if (incompatibleOptionIds.includes(id)) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"CANNOT_MAKE_INCOMPATIBLE_WITH_SELF",
						);
					}

					{
						const [validOptions] = await this.innerSpace.getEntities({
							ids: incompatibleOptionIds,
						});

						if (validOptions.length !== incompatibleOptionIds.length) {
							return this.#businessError.error.UNKNOWN_ERROR(
								"INVALID_INCOMPATIBLE_OPTIONS",
							);
						}

						validOptions.forEach((option) => {
							optionGroupIds.add(option.group_id);
						});
					}
				}

				// check for required options exist
				if (requiredOptionIds) {
					if (requiredOptionIds.includes(id)) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"CANNOT_MAKE_REQUIRED_WITH_SELF",
						);
					}

					{
						const [validOptions] = await this.innerSpace.getEntities({
							ids: requiredOptionIds,
						});

						if (validOptions.length !== requiredOptionIds.length) {
							return this.#businessError.error.UNKNOWN_ERROR(
								"INVALID_REQUIRED_OPTIONS",
							);
						}

						validOptions.forEach((option) => {
							optionGroupIds.add(option.group_id);
						});
					}
				}

				// Проверим, что все optionGroupIds принадлежат позиции
				if (optionGroupIds.size) {
					const groups
						= await this.services.fabricatedProductOptionGroup.innerSpace.getEntitiesForCheck(
							{
								ids: Array.from(optionGroupIds),
							},
						);

					if (
						groups.some(
							(group) =>
								group.nomenclature_item_id !== groupData.nomenclature_item_id,
						)
					) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"ONLY_SELF_OPTIONS_CAN_BE_CONTROLLED",
						);
					}
				}

				return { data: true };
			},

			remove: async (
				payload: WarehouseDomain.FabricatedProductOption.Remove.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const entity = await this.#getEntityForCheck({ id: payload.id });

				if (entity.error) return { error: entity.error };

				return { data: true };
			},

			selectActualVersion: async (
				payload: WarehouseDomain.FabricatedProductOption.SelectActualVersion.Params,
			): Promise<Types.Common.TDataError<true>> => {
				const { headerId, id } = payload;

				// check for entity exists
				{
					const { error } = await this.innerSpace.getEntityForCheck({
						id,
					});

					if (error) return { error };
				}

				// check for header exists
				{
					const { data, error }
						= await this.services.optionAdjustHeader.innerSpace.getEntityForCheck(
							{
								id: headerId,
							},
						);

					if (error) return { error };

					if (
						data.system_status !== optionAdjustHeaderSystemStatus["accepted"]
					) {
						return this.#businessError.error.UNKNOWN_ERROR(
							"OPTION_ADJUST_HEADER_MUST_BE_ACCEPTED",
						);
					}
				}

				return { data: true };
			},
		},

		isForNomenclatureItem: async (payload: {
			ids: string[];
			nomenclatureItemId: string;
		}): Promise<Types.Common.TDataError<true>> => {
			const entities = await this.#getEntitiesForCheck(payload);
			const groupIds = [...new Set(entities.map((e) => e.group_id))];
			const groups
				= await this.services.fabricatedProductOptionGroup.innerSpace.getEntitiesForCheck(
					{
						ids: groupIds,
					},
				);

			for (const group of groups) {
				if (group.nomenclature_item_id !== payload.nomenclatureItemId) {
					return this.#businessError.error.UNKNOWN_ERROR(
						"OPTIONS_NOT_FOR_NOMENCLATURE_ITEM",
					);
				}
			}

			return { data: true };
		},
	};

	async #getEntityForCheck(payload: {
		id: string;
	}): Promise<
		Types.Common.TDataError<Types.Dal.FabricatedProductOption.Types.EntityForCheck>
	> {
		const entity = await this.#repository.getEntityForCheck(payload);

		if (!entity) {
			return this.#businessError.error.ENTITY_NOT_FOUND(
				"NOT_FOUND_FABRICATED_PRODUCT_OPTION",
			);
		}

		return { data: entity };
	}

	async #getEntity(payload: {
		id: string;
	}): Promise<
		Types.Common.TDataError<Types.Dal.FabricatedProductOption.Types.Entity>
	> {
		const entity = await this.#repository.getEntity(payload);

		if (!entity) {
			return this.#businessError.error.ENTITY_NOT_FOUND(
				"NOT_FOUND_FABRICATED_PRODUCT_OPTION",
			);
		}

		return { data: entity };
	}

	async #getEntitiesForCheck(payload: {
		ids: string[];
	}): Promise<Types.Dal.FabricatedProductOption.Types.EntityForCheck[]> {
		return await this.#repository.getEntitiesForCheck(payload);
	}

	innerSpace = {
		getEntity: this.#getEntity.bind(this),

		getEntityForCheck: async (payload: { id: string; }) =>
			this.#getEntityForCheck(payload),

		getEntitiesForCheck: async (payload: { ids: string[]; }) =>
			this.#getEntitiesForCheck(payload),

		getEntities: async (payload: {
			ids: string[];
			nomenclatureItemIds?: string[];
		}) => this.#repository.getList({ params: payload }),

		checkIsForNomenclatureItem: async (payload: {
			ids: string[];
			nomenclatureItemId: string;
		}) => this.#check.isForNomenclatureItem(payload),
	};

	queueJobHandlers = {
		create: async (
			payload: WarehouseDomain.FabricatedProductOption.Create.Params,
		): Promise<Types.Common.TDataError<{ id: string; }>> => {
			const check = await this.#check.before.create(payload);

			if (check.error) return { error: check.error };

			const orderNumber = await this.#repository.getNextOrderNumber({
				groupId: payload.groupId,
			});

			const created = await this.#transactions[
				"fabricated-product-option-create"
			]({
				...payload,
				orderNumber,
			});

			return {
				data: created,
			};
		},

		update: async (
			payload: WarehouseDomain.FabricatedProductOption.Update.Params,
		): Promise<Types.Common.TDataError<{ id: string; }>> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			await this.#transactions["fabricated-product-option-update"](payload);

			return {
				data: {
					id: payload.id,
				},
			};
		},

		hide: async (
			payload: WarehouseDomain.FabricatedProductOption.Hide.Params,
		): Promise<Types.Common.TDataError<{ id: string; }>> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			await this.#repository.updateOneByPk(payload.id, {
				is_hidden: true,
			});

			return {
				data: {
					id: payload.id,
				},
			};
		},

		unhide: async (
			payload: WarehouseDomain.FabricatedProductOption.Unhide.Params,
		): Promise<Types.Common.TDataError<{ id: string; }>> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			await this.#repository.updateOneByPk(payload.id, {
				is_hidden: false,
			});

			return {
				data: {
					id: payload.id,
				},
			};
		},
	};

	outerSpace = {
		create: async (
			payload: WarehouseDomain.FabricatedProductOption.Create.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.Create.Result>
		> => {
			const check = await this.#check.before.create(payload);

			if (check.error) return { error: check.error };

			const created = await this.services.queueManager.queues[
				"fabricated-product-option-operations"
			].exec({
				type: "create",
				payload,
			});

			if (created.error) return { error: created.error };

			const entity = await this.#getEntity({ id: created.data.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		getEntity: async (
			payload: WarehouseDomain.FabricatedProductOption.GetEntity.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.GetEntity.Result>
		> => {
			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		getList: async (
			payload: WarehouseDomain.FabricatedProductOption.GetList.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.GetList.Result>
		> => {
			const { filters, limit, offset, order } = payload;

			const [list, total] = await this.#repository.getList({
				order,
				pagination: { limit, offset },
				params: filters,
			});

			return {
				data: {
					list: list.map(Response.getListEntity),
					total,
				},
			};
		},

		getPreparedList: async (
			payload: WarehouseDomain.FabricatedProductOption.GetPreparedList.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.GetPreparedList.Result>
		> => {
			const { filters, limit, offset, order } = payload;

			const [list, total] = await this.#repository.getList({
				order,
				pagination: { limit, offset },
				params: filters,
			});

			return {
				data: {
					list: list.map(Response.getPreparedListEntity),
					total,
				},
			};
		},

		getParentList: async (
			payload: WarehouseDomain.FabricatedProductOption.GetParentList.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.GetParentList.Result>
		> => {
			const { docBillOfMaterialHeaderId, id } = payload;

			if (id || !docBillOfMaterialHeaderId) {
				return this.#businessError.error.WORK_IN_PROGRESS();
			}

			const options = await this.#repository.getParentList({
				docBillOfMaterialHeaderId,
			});

			return { data: options.map(Response.getParentListEntity) };
		},

		update: async (
			payload: WarehouseDomain.FabricatedProductOption.Update.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.Update.Result>
		> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			const updated = await this.services.queueManager.queues[
				"fabricated-product-option-operations"
			].exec({
				type: "update",
				payload,
			});

			if (updated.error) return { error: updated.error };

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		hide: async (
			payload: WarehouseDomain.FabricatedProductOption.Hide.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.Hide.Result>
		> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			const updated = await this.services.queueManager.queues[
				"fabricated-product-option-operations"
			].exec({
				type: "hide",
				payload,
			});

			if (updated.error) return { error: updated.error };

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		unhide: async (
			payload: WarehouseDomain.FabricatedProductOption.Unhide.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.Unhide.Result>
		> => {
			const check = await this.#check.before.update(payload);

			if (check.error) return { error: check.error };

			const updated = await this.services.queueManager.queues[
				"fabricated-product-option-operations"
			].exec({
				type: "unhide",
				payload,
			});

			if (updated.error) return { error: updated.error };

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},

		remove: async (
			payload: WarehouseDomain.FabricatedProductOption.Remove.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.Remove.Result>
		> => {
			const check = await this.#check.before.remove(payload);

			if (check.error) return { error: check.error };

			return this.#businessError.error.WORK_IN_PROGRESS();

			/* await this.#transactions["fabricated-product-option-remove"]({
				ids: [payload.id],
			});

			return {
				data: {
					id: payload.id,
				},
			}; */
		},

		selectActualVersion: async (
			payload: WarehouseDomain.FabricatedProductOption.SelectActualVersion.Params,
		): Promise<
			Types.Common.TDataError<WarehouseDomain.FabricatedProductOption.SelectActualVersion.Result>
		> => {
			const check = await this.#check.before.selectActualVersion(payload);

			if (check.error) return { error: check.error };

			await this.#repository.updateOneByPk(payload.id, {
				actual_adjust_header_id: payload.headerId,
			});

			const entity = await this.#getEntity({ id: payload.id });

			if (entity.error) return { error: entity.error };

			return { data: Response.getEntity(entity.data) };
		},
	};
}
