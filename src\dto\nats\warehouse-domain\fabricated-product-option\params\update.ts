import { Static, Type } from "@sinclair/typebox";

import * as BaseEntities from "../../../../../base-entities";
import { <PERSON>hemaHel<PERSON> } from "../../../../../common";

const FabricatedProductOption = BaseEntities.WarehouseDomain.FabricatedProductOption.entity;

export const update = SchemaHelper.StrictObject({
	id: FabricatedProductOption.id,

	title: Type.Optional(FabricatedProductOption.title),
	description: Type.Optional(FabricatedProductOption.description),
	mnemonics: Type.Optional(FabricatedProductOption.mnemonics),
	priceModifier: Type.Optional(FabricatedProductOption.priceModifier),

	orderNumber: Type.Optional(FabricatedProductOption.orderNumber),

	requiredOptionIds: Type.Optional(FabricatedProductOption.requiredOptionIds),
	incompatibleOptionIds: Type.Optional(FabricatedProductOption.incompatibleOptionIds),
});

export type Update = Static<typeof update>;
