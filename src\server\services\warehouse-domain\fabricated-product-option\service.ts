import { FabricatedProductOption } from "@2people-it/inwave-erp-types/dist/protocols/http/domains/warehouse-domain/index.js";
import InwaveTypes from "@2people-it/inwave-erp-types";

import * as Types from "../../../types/index.js";

import BaseService from "../../base-service.js";

export default class Service extends BaseService {
	#broker;
	#logger;

	constructor(data: {
		broker: Types.Broker.default;
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#broker = data.broker;
		this.#logger = data.logger;
	}

	outerSpace = {
		create: async (
			payload: FabricatedProductOption.Create.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.Create.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/create",
				params: payload,
				meta,
			});

			return { data: result };
		},

		getEntity: async (
			payload: FabricatedProductOption.GetEntity.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.GetEntity.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/get-entity",
				params: payload,
				meta,
			});

			return { data: result };
		},

		getPreparedList: async (
			payload: FabricatedProductOption.GetPreparedList.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.GetPreparedList.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/get-prepared-list",
				params: payload,
				meta,
			});

			return { data: result };
		},

		update: async (
			payload: FabricatedProductOption.Update.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.Update.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/update",
				params: payload,
				meta,
			});

			return { data: result };
		},

		hide: async (
			payload: FabricatedProductOption.Hide.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.Hide.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/hide",
				params: payload,
				meta,
			});

			return { data: result };
		},

		unhide: async (
			payload: FabricatedProductOption.Unhide.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.Unhide.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/unhide",
				params: payload,
				meta,
			});

			return { data: result };
		},

		remove: async (
			payload: FabricatedProductOption.Remove.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.Remove.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/remove",
				params: payload,
				meta,
			});

			return { data: result };
		},

		selectActualVersion: async (
			payload: FabricatedProductOption.SelectActualVersion.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.SelectActualVersion.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/select-actual-version",
				params: payload,
				meta,
			});

			return { data: result };
		},

		getParentList: async (
			payload: FabricatedProductOption.GetParentList.Params,
			meta?: InwaveTypes.Common.Types.HttpProtocolApiMeta,
		): Promise<Types.Common.TDataError<FabricatedProductOption.GetParentList.Result>> => {
			const result = await this.#broker.domains.warehouse.fetch({
				methodName: "warehouse-domain:fabricated-product-option/get-parent-list",
				params: payload,
				meta,
			});

			return { data: result };
		},
	};
}
