import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@2people-it/inwave-ms-wrapper";
import { NatsProtocol } from "@2people-it/inwave-erp-types";
import { PoolClient } from "@2people-it/inwave-ms-wrapper/build/lib/transactions/types/index.js";

import * as Models from "../../models/index.js";

import * as Types from "../../../types/index.js";

export type DocInvoicePurchaseHeaderCreate = {
	create: Models.DocInvoicePurchaseHeader.Types.CreateFields;
	textPrefix: string;
	transactionClient?: PoolClient;
};

export type DocInvoicePurchaseItemsSave = {
	docInvoicePurchaseHeaderId: string;
	items: Omit<
		Models.DocInvoicePurchaseItem.Types.CreateFields,
		"doc_invoice_purchase_header_id"
	>[] &
	{
		options:
				| {
					id: string;
					header_id: string;
				  }[]
				| null;
	}[];
	transactionClient?: PoolClient;
};

export type DocInvoicePurchaseHeaderAccept = {
	acceptorId: string;
	docInvoicePurchaseHeaderId: string;
	totalQuantity: string;
	totalCostAmount: string;
	positionCount: number;
	companyId: string;
	specItems: Types.Dal.DocInvoicePurchaseItem.Types.ListEntity[];
	nomenclatureItems: NatsProtocol.Domains.WarehouseDomain.NomenclatureItem.GetList.Result["list"];
	relatedEntityService: Types.ServiceLocator.default["services"]["relatedEntity"];
	basisDocument?: {
		docHeaderId: string;
		docType: "whs004";
	};
	transactionClient?: PoolClient;
};

export type IncomingPartCreate = {
	header: {
		authorId: string;
		companyId: string;
		incomingDocId: string;
		incomingDocType: string;
		warehouseId: string;
		relevanceDate: string;
	};
	items: Array<{
		nomenclatureItemId: string;
		fabricatedProductId: string | null;
		freeQuantity: string;
		incomingItemPrice: string;
		incomingQuantity: string;
		outgoingQuantity: string;
	}>;
};

export type DocInvoicePurchaseHeaderDecline = {
	docInvoicePurchaseHeaderId: string;
};

export type OutgoingPartCreate = {
	header: {
		authorId: string;
		companyId: string;
		outgoingDocId: string;
		outgoingDocType: string;
		warehouseId: string;
		relevanceDate: Date;
	};
	items: Array<{
		nomenclatureItemId: string;
		fabricatedProductId: string | null;
		quantity: string;
		sellingPrice: string;
		costPrice: string;
	}>;
};

export type WarehouseGetBasicBalance = {
	order?: NatsProtocol.Domains.WarehouseDomain.Warehouse.GetBasicBalance.Params["order"];
	pagination?: DbManager.Types.TPagination;
	params: {
		relevanceDate: string;
		warehouseIds?: string[];
		nomenclatureItemIds?: string[];
		fabricatedProductIds?: string[];
	};
};

export type FabricatedProductOptionCreate = {
	groupId: string;
	title: string;
	description: string | null;
	mnemonics: string;
	priceModifier: string;
	requiredOptionIds: string[];
	incompatibleOptionIds: string[];
	isHidden: boolean;
	orderNumber: number;
};

export type FabricatedProductOptionUpdate = {
	id: string;
	title?: string;
	description?: string | null;
	mnemonics?: string;
	priceModifier?: string;
	requiredOptionIds?: string[];
	incompatibleOptionIds?: string[];
	orderNumber?: number;
};

export type FabricatedProductOptionHide = {
	id: string;
};

export type FabricatedProductOptionUnhide = {
	id: string;
};

export type FabricatedProductOptionRemove = {
	ids: string[];
};

export type FabricatedProductOptionGroupUpdate = {
	id: string;
	title?: string;
	description?: string | null;
	type?: number;
	isSelectedValueRequired?: boolean;
	orderNumber?: number;
};

export type FabricatedProductOptionGroupHide = {
	id: string;
};

export type FabricatedProductOptionGroupUnhide = {
	id: string;
};

export type FabricatedProductOptionGroupRemove = {
	id: string;
};

export * from "./option-adjust-header.js";

export type NomenclatureItemGetBalanceData = {
	warehouseIds?: string[];
	configurations: {
		nomenclatureItemId: string;
		docBillOfMaterialHeaderId: string | null;
		optionsHash: string | null;
	}[];
};
